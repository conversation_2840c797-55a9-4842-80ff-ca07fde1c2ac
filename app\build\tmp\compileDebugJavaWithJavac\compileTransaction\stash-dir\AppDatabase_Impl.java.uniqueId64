package com.example.timemate.data.database;

import androidx.annotation.NonNull;
import androidx.room.DatabaseConfiguration;
import androidx.room.InvalidationTracker;
import androidx.room.RoomDatabase;
import androidx.room.RoomOpenHelper;
import androidx.room.migration.AutoMigrationSpec;
import androidx.room.migration.Migration;
import androidx.room.util.DBUtil;
import androidx.room.util.TableInfo;
import androidx.sqlite.db.SupportSQLiteDatabase;
import androidx.sqlite.db.SupportSQLiteOpenHelper;
import com.example.timemate.ScheduleReminderDao;
import com.example.timemate.ScheduleReminderDao_Impl;
import com.example.timemate.data.database.dao.FriendDao;
import com.example.timemate.data.database.dao.FriendDao_Impl;
import com.example.timemate.data.database.dao.ScheduleDao;
import com.example.timemate.data.database.dao.ScheduleDao_Impl;
import com.example.timemate.data.database.dao.SharedScheduleDao;
import com.example.timemate.data.database.dao.SharedScheduleDao_Impl;
import com.example.timemate.data.database.dao.UserDao;
import com.example.timemate.data.database.dao.UserDao_Impl;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class AppDatabase_Impl extends AppDatabase {
  private volatile ScheduleDao _scheduleDao;

  private volatile UserDao _userDao;

  private volatile FriendDao _friendDao;

  private volatile SharedScheduleDao _sharedScheduleDao;

  private volatile ScheduleReminderDao _scheduleReminderDao;

  @Override
  @NonNull
  protected SupportSQLiteOpenHelper createOpenHelper(@NonNull final DatabaseConfiguration config) {
    final SupportSQLiteOpenHelper.Callback _openCallback = new RoomOpenHelper(config, new RoomOpenHelper.Delegate(6) {
      @Override
      public void createAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("CREATE TABLE IF NOT EXISTS `schedules` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `userId` TEXT NOT NULL, `title` TEXT NOT NULL, `date` TEXT NOT NULL, `time` TEXT NOT NULL, `departure` TEXT, `destination` TEXT, `memo` TEXT, `routeInfo` TEXT, `selectedTransportModes` TEXT, `isCompleted` INTEGER NOT NULL, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `users` (`userId` TEXT NOT NULL, `nickname` TEXT NOT NULL, `email` TEXT, `password` TEXT, `profileImage` TEXT, `createdAt` INTEGER NOT NULL, `lastLoginAt` INTEGER NOT NULL, `isActive` INTEGER NOT NULL, PRIMARY KEY(`userId`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `friends` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `userId` TEXT NOT NULL, `friendUserId` TEXT NOT NULL, `friendNickname` TEXT NOT NULL, `createdAt` INTEGER NOT NULL, `isAccepted` INTEGER NOT NULL, `isBlocked` INTEGER NOT NULL)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `shared_schedules` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `originalScheduleId` INTEGER NOT NULL, `creatorUserId` TEXT, `creatorNickname` TEXT, `invitedUserId` TEXT, `invitedNickname` TEXT, `title` TEXT, `date` TEXT, `time` TEXT, `departure` TEXT, `destination` TEXT, `memo` TEXT, `status` TEXT, `isNotificationSent` INTEGER NOT NULL, `isNotificationRead` INTEGER NOT NULL, `sharedScheduleId` INTEGER NOT NULL, `isSyncEnabled` INTEGER NOT NULL, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, `respondedAt` INTEGER NOT NULL)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `schedule_reminder` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `schedule_id` INTEGER NOT NULL, `user_id` TEXT, `title` TEXT, `appointment_time` TEXT, `departure` TEXT, `destination` TEXT, `optimal_transport` TEXT, `duration_minutes` INTEGER NOT NULL, `recommended_departure_time` TEXT, `distance` TEXT, `route_summary` TEXT, `toll_fare` TEXT, `fuel_price` TEXT, `created_at` INTEGER NOT NULL, `notification_sent` INTEGER NOT NULL, `is_active` INTEGER NOT NULL)");
        db.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)");
        db.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '4bdde4500dcd077f0163b427f8cb8c13')");
      }

      @Override
      public void dropAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("DROP TABLE IF EXISTS `schedules`");
        db.execSQL("DROP TABLE IF EXISTS `users`");
        db.execSQL("DROP TABLE IF EXISTS `friends`");
        db.execSQL("DROP TABLE IF EXISTS `shared_schedules`");
        db.execSQL("DROP TABLE IF EXISTS `schedule_reminder`");
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onDestructiveMigration(db);
          }
        }
      }

      @Override
      public void onCreate(@NonNull final SupportSQLiteDatabase db) {
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onCreate(db);
          }
        }
      }

      @Override
      public void onOpen(@NonNull final SupportSQLiteDatabase db) {
        mDatabase = db;
        internalInitInvalidationTracker(db);
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onOpen(db);
          }
        }
      }

      @Override
      public void onPreMigrate(@NonNull final SupportSQLiteDatabase db) {
        DBUtil.dropFtsSyncTriggers(db);
      }

      @Override
      public void onPostMigrate(@NonNull final SupportSQLiteDatabase db) {
      }

      @Override
      @NonNull
      public RoomOpenHelper.ValidationResult onValidateSchema(
          @NonNull final SupportSQLiteDatabase db) {
        final HashMap<String, TableInfo.Column> _columnsSchedules = new HashMap<String, TableInfo.Column>(13);
        _columnsSchedules.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedules.put("userId", new TableInfo.Column("userId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedules.put("title", new TableInfo.Column("title", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedules.put("date", new TableInfo.Column("date", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedules.put("time", new TableInfo.Column("time", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedules.put("departure", new TableInfo.Column("departure", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedules.put("destination", new TableInfo.Column("destination", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedules.put("memo", new TableInfo.Column("memo", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedules.put("routeInfo", new TableInfo.Column("routeInfo", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedules.put("selectedTransportModes", new TableInfo.Column("selectedTransportModes", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedules.put("isCompleted", new TableInfo.Column("isCompleted", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedules.put("createdAt", new TableInfo.Column("createdAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedules.put("updatedAt", new TableInfo.Column("updatedAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysSchedules = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesSchedules = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoSchedules = new TableInfo("schedules", _columnsSchedules, _foreignKeysSchedules, _indicesSchedules);
        final TableInfo _existingSchedules = TableInfo.read(db, "schedules");
        if (!_infoSchedules.equals(_existingSchedules)) {
          return new RoomOpenHelper.ValidationResult(false, "schedules(com.example.timemate.data.model.Schedule).\n"
                  + " Expected:\n" + _infoSchedules + "\n"
                  + " Found:\n" + _existingSchedules);
        }
        final HashMap<String, TableInfo.Column> _columnsUsers = new HashMap<String, TableInfo.Column>(8);
        _columnsUsers.put("userId", new TableInfo.Column("userId", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("nickname", new TableInfo.Column("nickname", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("email", new TableInfo.Column("email", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("password", new TableInfo.Column("password", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("profileImage", new TableInfo.Column("profileImage", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("createdAt", new TableInfo.Column("createdAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("lastLoginAt", new TableInfo.Column("lastLoginAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("isActive", new TableInfo.Column("isActive", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysUsers = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesUsers = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoUsers = new TableInfo("users", _columnsUsers, _foreignKeysUsers, _indicesUsers);
        final TableInfo _existingUsers = TableInfo.read(db, "users");
        if (!_infoUsers.equals(_existingUsers)) {
          return new RoomOpenHelper.ValidationResult(false, "users(com.example.timemate.data.model.User).\n"
                  + " Expected:\n" + _infoUsers + "\n"
                  + " Found:\n" + _existingUsers);
        }
        final HashMap<String, TableInfo.Column> _columnsFriends = new HashMap<String, TableInfo.Column>(7);
        _columnsFriends.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFriends.put("userId", new TableInfo.Column("userId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFriends.put("friendUserId", new TableInfo.Column("friendUserId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFriends.put("friendNickname", new TableInfo.Column("friendNickname", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFriends.put("createdAt", new TableInfo.Column("createdAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFriends.put("isAccepted", new TableInfo.Column("isAccepted", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFriends.put("isBlocked", new TableInfo.Column("isBlocked", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysFriends = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesFriends = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoFriends = new TableInfo("friends", _columnsFriends, _foreignKeysFriends, _indicesFriends);
        final TableInfo _existingFriends = TableInfo.read(db, "friends");
        if (!_infoFriends.equals(_existingFriends)) {
          return new RoomOpenHelper.ValidationResult(false, "friends(com.example.timemate.data.model.Friend).\n"
                  + " Expected:\n" + _infoFriends + "\n"
                  + " Found:\n" + _existingFriends);
        }
        final HashMap<String, TableInfo.Column> _columnsSharedSchedules = new HashMap<String, TableInfo.Column>(20);
        _columnsSharedSchedules.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSharedSchedules.put("originalScheduleId", new TableInfo.Column("originalScheduleId", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSharedSchedules.put("creatorUserId", new TableInfo.Column("creatorUserId", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSharedSchedules.put("creatorNickname", new TableInfo.Column("creatorNickname", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSharedSchedules.put("invitedUserId", new TableInfo.Column("invitedUserId", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSharedSchedules.put("invitedNickname", new TableInfo.Column("invitedNickname", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSharedSchedules.put("title", new TableInfo.Column("title", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSharedSchedules.put("date", new TableInfo.Column("date", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSharedSchedules.put("time", new TableInfo.Column("time", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSharedSchedules.put("departure", new TableInfo.Column("departure", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSharedSchedules.put("destination", new TableInfo.Column("destination", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSharedSchedules.put("memo", new TableInfo.Column("memo", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSharedSchedules.put("status", new TableInfo.Column("status", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSharedSchedules.put("isNotificationSent", new TableInfo.Column("isNotificationSent", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSharedSchedules.put("isNotificationRead", new TableInfo.Column("isNotificationRead", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSharedSchedules.put("sharedScheduleId", new TableInfo.Column("sharedScheduleId", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSharedSchedules.put("isSyncEnabled", new TableInfo.Column("isSyncEnabled", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSharedSchedules.put("createdAt", new TableInfo.Column("createdAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSharedSchedules.put("updatedAt", new TableInfo.Column("updatedAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSharedSchedules.put("respondedAt", new TableInfo.Column("respondedAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysSharedSchedules = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesSharedSchedules = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoSharedSchedules = new TableInfo("shared_schedules", _columnsSharedSchedules, _foreignKeysSharedSchedules, _indicesSharedSchedules);
        final TableInfo _existingSharedSchedules = TableInfo.read(db, "shared_schedules");
        if (!_infoSharedSchedules.equals(_existingSharedSchedules)) {
          return new RoomOpenHelper.ValidationResult(false, "shared_schedules(com.example.timemate.data.model.SharedSchedule).\n"
                  + " Expected:\n" + _infoSharedSchedules + "\n"
                  + " Found:\n" + _existingSharedSchedules);
        }
        final HashMap<String, TableInfo.Column> _columnsScheduleReminder = new HashMap<String, TableInfo.Column>(17);
        _columnsScheduleReminder.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScheduleReminder.put("schedule_id", new TableInfo.Column("schedule_id", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScheduleReminder.put("user_id", new TableInfo.Column("user_id", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScheduleReminder.put("title", new TableInfo.Column("title", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScheduleReminder.put("appointment_time", new TableInfo.Column("appointment_time", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScheduleReminder.put("departure", new TableInfo.Column("departure", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScheduleReminder.put("destination", new TableInfo.Column("destination", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScheduleReminder.put("optimal_transport", new TableInfo.Column("optimal_transport", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScheduleReminder.put("duration_minutes", new TableInfo.Column("duration_minutes", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScheduleReminder.put("recommended_departure_time", new TableInfo.Column("recommended_departure_time", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScheduleReminder.put("distance", new TableInfo.Column("distance", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScheduleReminder.put("route_summary", new TableInfo.Column("route_summary", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScheduleReminder.put("toll_fare", new TableInfo.Column("toll_fare", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScheduleReminder.put("fuel_price", new TableInfo.Column("fuel_price", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScheduleReminder.put("created_at", new TableInfo.Column("created_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScheduleReminder.put("notification_sent", new TableInfo.Column("notification_sent", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScheduleReminder.put("is_active", new TableInfo.Column("is_active", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysScheduleReminder = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesScheduleReminder = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoScheduleReminder = new TableInfo("schedule_reminder", _columnsScheduleReminder, _foreignKeysScheduleReminder, _indicesScheduleReminder);
        final TableInfo _existingScheduleReminder = TableInfo.read(db, "schedule_reminder");
        if (!_infoScheduleReminder.equals(_existingScheduleReminder)) {
          return new RoomOpenHelper.ValidationResult(false, "schedule_reminder(com.example.timemate.ScheduleReminder).\n"
                  + " Expected:\n" + _infoScheduleReminder + "\n"
                  + " Found:\n" + _existingScheduleReminder);
        }
        return new RoomOpenHelper.ValidationResult(true, null);
      }
    }, "4bdde4500dcd077f0163b427f8cb8c13", "056dc5b8eac8f7cc77a3a2a5668f0ea7");
    final SupportSQLiteOpenHelper.Configuration _sqliteConfig = SupportSQLiteOpenHelper.Configuration.builder(config.context).name(config.name).callback(_openCallback).build();
    final SupportSQLiteOpenHelper _helper = config.sqliteOpenHelperFactory.create(_sqliteConfig);
    return _helper;
  }

  @Override
  @NonNull
  protected InvalidationTracker createInvalidationTracker() {
    final HashMap<String, String> _shadowTablesMap = new HashMap<String, String>(0);
    final HashMap<String, Set<String>> _viewTables = new HashMap<String, Set<String>>(0);
    return new InvalidationTracker(this, _shadowTablesMap, _viewTables, "schedules","users","friends","shared_schedules","schedule_reminder");
  }

  @Override
  public void clearAllTables() {
    super.assertNotMainThread();
    final SupportSQLiteDatabase _db = super.getOpenHelper().getWritableDatabase();
    try {
      super.beginTransaction();
      _db.execSQL("DELETE FROM `schedules`");
      _db.execSQL("DELETE FROM `users`");
      _db.execSQL("DELETE FROM `friends`");
      _db.execSQL("DELETE FROM `shared_schedules`");
      _db.execSQL("DELETE FROM `schedule_reminder`");
      super.setTransactionSuccessful();
    } finally {
      super.endTransaction();
      _db.query("PRAGMA wal_checkpoint(FULL)").close();
      if (!_db.inTransaction()) {
        _db.execSQL("VACUUM");
      }
    }
  }

  @Override
  @NonNull
  protected Map<Class<?>, List<Class<?>>> getRequiredTypeConverters() {
    final HashMap<Class<?>, List<Class<?>>> _typeConvertersMap = new HashMap<Class<?>, List<Class<?>>>();
    _typeConvertersMap.put(ScheduleDao.class, ScheduleDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(UserDao.class, UserDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(FriendDao.class, FriendDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(SharedScheduleDao.class, SharedScheduleDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(ScheduleReminderDao.class, ScheduleReminderDao_Impl.getRequiredConverters());
    return _typeConvertersMap;
  }

  @Override
  @NonNull
  public Set<Class<? extends AutoMigrationSpec>> getRequiredAutoMigrationSpecs() {
    final HashSet<Class<? extends AutoMigrationSpec>> _autoMigrationSpecsSet = new HashSet<Class<? extends AutoMigrationSpec>>();
    return _autoMigrationSpecsSet;
  }

  @Override
  @NonNull
  public List<Migration> getAutoMigrations(
      @NonNull final Map<Class<? extends AutoMigrationSpec>, AutoMigrationSpec> autoMigrationSpecs) {
    final List<Migration> _autoMigrations = new ArrayList<Migration>();
    return _autoMigrations;
  }

  @Override
  public ScheduleDao scheduleDao() {
    if (_scheduleDao != null) {
      return _scheduleDao;
    } else {
      synchronized(this) {
        if(_scheduleDao == null) {
          _scheduleDao = new ScheduleDao_Impl(this);
        }
        return _scheduleDao;
      }
    }
  }

  @Override
  public UserDao userDao() {
    if (_userDao != null) {
      return _userDao;
    } else {
      synchronized(this) {
        if(_userDao == null) {
          _userDao = new UserDao_Impl(this);
        }
        return _userDao;
      }
    }
  }

  @Override
  public FriendDao friendDao() {
    if (_friendDao != null) {
      return _friendDao;
    } else {
      synchronized(this) {
        if(_friendDao == null) {
          _friendDao = new FriendDao_Impl(this);
        }
        return _friendDao;
      }
    }
  }

  @Override
  public SharedScheduleDao sharedScheduleDao() {
    if (_sharedScheduleDao != null) {
      return _sharedScheduleDao;
    } else {
      synchronized(this) {
        if(_sharedScheduleDao == null) {
          _sharedScheduleDao = new SharedScheduleDao_Impl(this);
        }
        return _sharedScheduleDao;
      }
    }
  }

  @Override
  public ScheduleReminderDao scheduleReminderDao() {
    if (_scheduleReminderDao != null) {
      return _scheduleReminderDao;
    } else {
      synchronized(this) {
        if(_scheduleReminderDao == null) {
          _scheduleReminderDao = new ScheduleReminderDao_Impl(this);
        }
        return _scheduleReminderDao;
      }
    }
  }
}
