<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="4dp"
    android:layout_marginVertical="6dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="2dp"
    app:cardBackgroundColor="@color/background_card"
    app:strokeWidth="0dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="20dp"
        android:gravity="center_vertical">

        <!-- iOS 스타일 장소 사진 -->
        <FrameLayout
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginEnd="16dp">

            <!-- 실제 장소 사진 -->
            <ImageView
                android:id="@+id/imagePlacePhoto"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:background="@drawable/ios_image_placeholder"
                android:src="@drawable/ic_map_placeholder" />

            <!-- 카테고리 아이콘 (오버레이) -->
            <TextView
                android:id="@+id/textPlaceIcon"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="bottom|end"
                android:layout_margin="6dp"
                android:background="@drawable/ios_icon_background"
                android:gravity="center"
                android:text="🏪"
                android:textSize="10sp"
                android:elevation="4dp" />

        </FrameLayout>

        <!-- 장소 정보 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- iOS 스타일 장소명 -->
            <TextView
                android:id="@+id/textPlaceName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="장소명"
                android:textSize="17sp"
                android:textStyle="bold"
                android:fontFamily="@font/pretendard_bold"
                android:textColor="@color/text_primary"
                android:maxLines="1"
                android:ellipsize="end"
                android:layout_marginBottom="2dp" />

            <!-- iOS 스타일 카테고리 -->
            <TextView
                android:id="@+id/textPlaceCategory"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="카테고리"
                android:textSize="13sp"
                android:fontFamily="@font/pretendard_medium"
                android:textColor="@color/ios_blue"
                android:layout_marginBottom="4dp" />

            <!-- iOS 스타일 주소 -->
            <TextView
                android:id="@+id/textPlaceAddress"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="주소"
                android:textSize="12sp"
                android:fontFamily="@font/pretendard_regular"
                android:textColor="@color/text_tertiary"
                android:maxLines="1"
                android:ellipsize="end"
                android:layout_marginBottom="6dp" />

            <!-- iOS 스타일 평점 및 거리 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/textPlaceRating"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="⭐ 4.5"
                    android:textSize="12sp"
                    android:fontFamily="@font/pretendard_medium"
                    android:textColor="@color/ios_orange"
                    android:layout_marginEnd="12dp"
                    android:background="@drawable/ios_rating_background"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="2dp" />

                <TextView
                    android:id="@+id/textPlaceDistance"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="📍 500m"
                    android:textSize="12sp"
                    android:fontFamily="@font/pretendard_medium"
                    android:textColor="@color/ios_blue"
                    android:background="@drawable/ios_distance_background"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="2dp" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1" />

                <!-- iOS 스타일 길찾기 버튼 -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnNavigation"
                    android:layout_width="wrap_content"
                    android:layout_height="32dp"
                    android:text="길찾기"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:fontFamily="@font/pretendard_bold"
                    android:textColor="@color/ios_blue"
                    android:background="@drawable/ios_navigation_button"
                    android:minWidth="64dp"
                    android:paddingHorizontal="12dp"
                    app:cornerRadius="16dp"
                    app:strokeWidth="1dp"
                    app:strokeColor="@color/ios_blue"
                    app:rippleColor="@color/ios_blue_light" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
