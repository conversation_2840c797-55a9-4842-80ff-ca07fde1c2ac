package com.example.timemate.data.database.dao;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.timemate.data.model.Friend;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class FriendDao_Impl implements FriendDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<Friend> __insertionAdapterOfFriend;

  private final EntityDeletionOrUpdateAdapter<Friend> __deletionAdapterOfFriend;

  private final EntityDeletionOrUpdateAdapter<Friend> __updateAdapterOfFriend;

  private final SharedSQLiteStatement __preparedStmtOfUpdateFriendStatus;

  private final SharedSQLiteStatement __preparedStmtOfDeleteFriendship;

  public FriendDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfFriend = new EntityInsertionAdapter<Friend>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR ABORT INTO `friends` (`id`,`userId`,`friendUserId`,`friendNickname`,`createdAt`,`isAccepted`,`isBlocked`) VALUES (nullif(?, 0),?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement, final Friend entity) {
        statement.bindLong(1, entity.id);
        if (entity.userId == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.userId);
        }
        if (entity.friendUserId == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.friendUserId);
        }
        if (entity.friendNickname == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.friendNickname);
        }
        statement.bindLong(5, entity.createdAt);
        final int _tmp = entity.isAccepted ? 1 : 0;
        statement.bindLong(6, _tmp);
        final int _tmp_1 = entity.isBlocked ? 1 : 0;
        statement.bindLong(7, _tmp_1);
      }
    };
    this.__deletionAdapterOfFriend = new EntityDeletionOrUpdateAdapter<Friend>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `friends` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement, final Friend entity) {
        statement.bindLong(1, entity.id);
      }
    };
    this.__updateAdapterOfFriend = new EntityDeletionOrUpdateAdapter<Friend>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `friends` SET `id` = ?,`userId` = ?,`friendUserId` = ?,`friendNickname` = ?,`createdAt` = ?,`isAccepted` = ?,`isBlocked` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement, final Friend entity) {
        statement.bindLong(1, entity.id);
        if (entity.userId == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.userId);
        }
        if (entity.friendUserId == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.friendUserId);
        }
        if (entity.friendNickname == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.friendNickname);
        }
        statement.bindLong(5, entity.createdAt);
        final int _tmp = entity.isAccepted ? 1 : 0;
        statement.bindLong(6, _tmp);
        final int _tmp_1 = entity.isBlocked ? 1 : 0;
        statement.bindLong(7, _tmp_1);
        statement.bindLong(8, entity.id);
      }
    };
    this.__preparedStmtOfUpdateFriendStatus = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE friends SET isAccepted = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteFriendship = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM friends WHERE userId = ? AND friendUserId = ?";
        return _query;
      }
    };
  }

  @Override
  public long insert(final Friend friend) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      final long _result = __insertionAdapterOfFriend.insertAndReturnId(friend);
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public int delete(final Friend friend) {
    __db.assertNotSuspendingTransaction();
    int _total = 0;
    __db.beginTransaction();
    try {
      _total += __deletionAdapterOfFriend.handle(friend);
      __db.setTransactionSuccessful();
      return _total;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public int update(final Friend friend) {
    __db.assertNotSuspendingTransaction();
    int _total = 0;
    __db.beginTransaction();
    try {
      _total += __updateAdapterOfFriend.handle(friend);
      __db.setTransactionSuccessful();
      return _total;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public int updateFriendStatus(final long id, final boolean isAccepted) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateFriendStatus.acquire();
    int _argIndex = 1;
    final int _tmp = isAccepted ? 1 : 0;
    _stmt.bindLong(_argIndex, _tmp);
    _argIndex = 2;
    _stmt.bindLong(_argIndex, id);
    try {
      __db.beginTransaction();
      try {
        final int _result = _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
        return _result;
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfUpdateFriendStatus.release(_stmt);
    }
  }

  @Override
  public int deleteFriendship(final String userId, final String friendUserId) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteFriendship.acquire();
    int _argIndex = 1;
    if (userId == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, userId);
    }
    _argIndex = 2;
    if (friendUserId == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, friendUserId);
    }
    try {
      __db.beginTransaction();
      try {
        final int _result = _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
        return _result;
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfDeleteFriendship.release(_stmt);
    }
  }

  @Override
  public Friend getFriendById(final long id) {
    final String _sql = "SELECT * FROM friends WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
      final int _cursorIndexOfFriendUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "friendUserId");
      final int _cursorIndexOfFriendNickname = CursorUtil.getColumnIndexOrThrow(_cursor, "friendNickname");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
      final int _cursorIndexOfIsAccepted = CursorUtil.getColumnIndexOrThrow(_cursor, "isAccepted");
      final int _cursorIndexOfIsBlocked = CursorUtil.getColumnIndexOrThrow(_cursor, "isBlocked");
      final Friend _result;
      if (_cursor.moveToFirst()) {
        _result = new Friend();
        _result.id = _cursor.getInt(_cursorIndexOfId);
        if (_cursor.isNull(_cursorIndexOfUserId)) {
          _result.userId = null;
        } else {
          _result.userId = _cursor.getString(_cursorIndexOfUserId);
        }
        if (_cursor.isNull(_cursorIndexOfFriendUserId)) {
          _result.friendUserId = null;
        } else {
          _result.friendUserId = _cursor.getString(_cursorIndexOfFriendUserId);
        }
        if (_cursor.isNull(_cursorIndexOfFriendNickname)) {
          _result.friendNickname = null;
        } else {
          _result.friendNickname = _cursor.getString(_cursorIndexOfFriendNickname);
        }
        _result.createdAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsAccepted);
        _result.isAccepted = _tmp != 0;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsBlocked);
        _result.isBlocked = _tmp_1 != 0;
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<Friend> getFriendsByUserId(final String userId) {
    final String _sql = "SELECT * FROM friends WHERE userId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (userId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, userId);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
      final int _cursorIndexOfFriendUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "friendUserId");
      final int _cursorIndexOfFriendNickname = CursorUtil.getColumnIndexOrThrow(_cursor, "friendNickname");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
      final int _cursorIndexOfIsAccepted = CursorUtil.getColumnIndexOrThrow(_cursor, "isAccepted");
      final int _cursorIndexOfIsBlocked = CursorUtil.getColumnIndexOrThrow(_cursor, "isBlocked");
      final List<Friend> _result = new ArrayList<Friend>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final Friend _item;
        _item = new Friend();
        _item.id = _cursor.getInt(_cursorIndexOfId);
        if (_cursor.isNull(_cursorIndexOfUserId)) {
          _item.userId = null;
        } else {
          _item.userId = _cursor.getString(_cursorIndexOfUserId);
        }
        if (_cursor.isNull(_cursorIndexOfFriendUserId)) {
          _item.friendUserId = null;
        } else {
          _item.friendUserId = _cursor.getString(_cursorIndexOfFriendUserId);
        }
        if (_cursor.isNull(_cursorIndexOfFriendNickname)) {
          _item.friendNickname = null;
        } else {
          _item.friendNickname = _cursor.getString(_cursorIndexOfFriendNickname);
        }
        _item.createdAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsAccepted);
        _item.isAccepted = _tmp != 0;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsBlocked);
        _item.isBlocked = _tmp_1 != 0;
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public Friend getFriendship(final String userId, final String friendUserId) {
    final String _sql = "SELECT * FROM friends WHERE userId = ? AND friendUserId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (userId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, userId);
    }
    _argIndex = 2;
    if (friendUserId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, friendUserId);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
      final int _cursorIndexOfFriendUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "friendUserId");
      final int _cursorIndexOfFriendNickname = CursorUtil.getColumnIndexOrThrow(_cursor, "friendNickname");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
      final int _cursorIndexOfIsAccepted = CursorUtil.getColumnIndexOrThrow(_cursor, "isAccepted");
      final int _cursorIndexOfIsBlocked = CursorUtil.getColumnIndexOrThrow(_cursor, "isBlocked");
      final Friend _result;
      if (_cursor.moveToFirst()) {
        _result = new Friend();
        _result.id = _cursor.getInt(_cursorIndexOfId);
        if (_cursor.isNull(_cursorIndexOfUserId)) {
          _result.userId = null;
        } else {
          _result.userId = _cursor.getString(_cursorIndexOfUserId);
        }
        if (_cursor.isNull(_cursorIndexOfFriendUserId)) {
          _result.friendUserId = null;
        } else {
          _result.friendUserId = _cursor.getString(_cursorIndexOfFriendUserId);
        }
        if (_cursor.isNull(_cursorIndexOfFriendNickname)) {
          _result.friendNickname = null;
        } else {
          _result.friendNickname = _cursor.getString(_cursorIndexOfFriendNickname);
        }
        _result.createdAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsAccepted);
        _result.isAccepted = _tmp != 0;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsBlocked);
        _result.isBlocked = _tmp_1 != 0;
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<Friend> getFriendsByStatus(final String userId, final boolean isAccepted) {
    final String _sql = "SELECT * FROM friends WHERE userId = ? AND isAccepted = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (userId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, userId);
    }
    _argIndex = 2;
    final int _tmp = isAccepted ? 1 : 0;
    _statement.bindLong(_argIndex, _tmp);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
      final int _cursorIndexOfFriendUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "friendUserId");
      final int _cursorIndexOfFriendNickname = CursorUtil.getColumnIndexOrThrow(_cursor, "friendNickname");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
      final int _cursorIndexOfIsAccepted = CursorUtil.getColumnIndexOrThrow(_cursor, "isAccepted");
      final int _cursorIndexOfIsBlocked = CursorUtil.getColumnIndexOrThrow(_cursor, "isBlocked");
      final List<Friend> _result = new ArrayList<Friend>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final Friend _item;
        _item = new Friend();
        _item.id = _cursor.getInt(_cursorIndexOfId);
        if (_cursor.isNull(_cursorIndexOfUserId)) {
          _item.userId = null;
        } else {
          _item.userId = _cursor.getString(_cursorIndexOfUserId);
        }
        if (_cursor.isNull(_cursorIndexOfFriendUserId)) {
          _item.friendUserId = null;
        } else {
          _item.friendUserId = _cursor.getString(_cursorIndexOfFriendUserId);
        }
        if (_cursor.isNull(_cursorIndexOfFriendNickname)) {
          _item.friendNickname = null;
        } else {
          _item.friendNickname = _cursor.getString(_cursorIndexOfFriendNickname);
        }
        _item.createdAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsAccepted);
        _item.isAccepted = _tmp_1 != 0;
        final int _tmp_2;
        _tmp_2 = _cursor.getInt(_cursorIndexOfIsBlocked);
        _item.isBlocked = _tmp_2 != 0;
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public int getFriendCount(final String userId) {
    final String _sql = "SELECT COUNT(*) FROM friends WHERE userId = ? AND isAccepted = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (userId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, userId);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if (_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
