{"logs": [{"outputFile": "com.example.timemate.app-mergeDebugResources-39:/values-el/values-el.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1d447aa9b1396eb1d1443234471551a7\\transformed\\core-1.13.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "38,39,40,41,42,43,44,120", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3543,3641,3744,3844,3947,4055,4161,10596", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "3636,3739,3839,3942,4050,4156,4273,10692"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\02344620b027cd4b51022a2f63eb19e2\\transformed\\browser-1.4.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,272,397", "endColumns": "109,106,124,109", "endOffsets": "160,267,392,502"}, "to": {"startLines": "48,52,53,54", "startColumns": "4,4,4,4", "startOffsets": "4583,4922,5029,5154", "endColumns": "109,106,124,109", "endOffsets": "4688,5024,5149,5259"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3104310070aa521e0a811bb63bfe0177\\transformed\\material-1.12.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,270,358,444,529,625,712,814,931,1017,1080,1146,1246,1328,1391,1482,1545,1610,1672,1741,1803,1857,1995,2052,2113,2167,2240,2393,2478,2557,2653,2737,2821,2960,3041,3126,3267,3357,3443,3498,3549,3615,3693,3778,3849,3932,4004,4084,4164,4235,4342,4434,4506,4603,4700,4774,4848,4950,5006,5093,5165,5253,5345,5407,5471,5534,5604,5720,5829,5938,6043,6102,6157,6248,6336,6411", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,87,85,84,95,86,101,116,85,62,65,99,81,62,90,62,64,61,68,61,53,137,56,60,53,72,152,84,78,95,83,83,138,80,84,140,89,85,54,50,65,77,84,70,82,71,79,79,70,106,91,71,96,96,73,73,101,55,86,71,87,91,61,63,62,69,115,108,108,104,58,54,90,87,74,80", "endOffsets": "265,353,439,524,620,707,809,926,1012,1075,1141,1241,1323,1386,1477,1540,1605,1667,1736,1798,1852,1990,2047,2108,2162,2235,2388,2473,2552,2648,2732,2816,2955,3036,3121,3262,3352,3438,3493,3544,3610,3688,3773,3844,3927,3999,4079,4159,4230,4337,4429,4501,4598,4695,4769,4843,4945,5001,5088,5160,5248,5340,5402,5466,5529,5599,5715,5824,5933,6038,6097,6152,6243,6331,6406,6487"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,49,50,51,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3101,3189,3275,3360,3456,4278,4380,4497,4693,4756,4822,5264,5346,5409,5500,5563,5628,5690,5759,5821,5875,6013,6070,6131,6185,6258,6411,6496,6575,6671,6755,6839,6978,7059,7144,7285,7375,7461,7516,7567,7633,7711,7796,7867,7950,8022,8102,8182,8253,8360,8452,8524,8621,8718,8792,8866,8968,9024,9111,9183,9271,9363,9425,9489,9552,9622,9738,9847,9956,10061,10120,10175,10352,10440,10515", "endLines": "5,33,34,35,36,37,45,46,47,49,50,51,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,117,118,119", "endColumns": "12,87,85,84,95,86,101,116,85,62,65,99,81,62,90,62,64,61,68,61,53,137,56,60,53,72,152,84,78,95,83,83,138,80,84,140,89,85,54,50,65,77,84,70,82,71,79,79,70,106,91,71,96,96,73,73,101,55,86,71,87,91,61,63,62,69,115,108,108,104,58,54,90,87,74,80", "endOffsets": "315,3184,3270,3355,3451,3538,4375,4492,4578,4751,4817,4917,5341,5404,5495,5558,5623,5685,5754,5816,5870,6008,6065,6126,6180,6253,6406,6491,6570,6666,6750,6834,6973,7054,7139,7280,7370,7456,7511,7562,7628,7706,7791,7862,7945,8017,8097,8177,8248,8355,8447,8519,8616,8713,8787,8861,8963,9019,9106,9178,9266,9358,9420,9484,9547,9617,9733,9842,9951,10056,10115,10170,10261,10435,10510,10591"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2d4d96d74a14046b91d40cca3b0165b0\\transformed\\appcompat-1.7.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,939,1030,1123,1218,1312,1412,1505,1600,1697,1788,1879,1964,2075,2184,2286,2397,2507,2615,2786,2886", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "218,329,446,531,637,760,849,934,1025,1118,1213,1307,1407,1500,1595,1692,1783,1874,1959,2070,2179,2281,2392,2502,2610,2781,2881,2967"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "320,438,549,666,751,857,980,1069,1154,1245,1338,1433,1527,1627,1720,1815,1912,2003,2094,2179,2290,2399,2501,2612,2722,2830,3001,10266", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "433,544,661,746,852,975,1064,1149,1240,1333,1428,1522,1622,1715,1810,1907,1998,2089,2174,2285,2394,2496,2607,2717,2825,2996,3096,10347"}}]}]}