<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 그림자 효과 -->
    <item android:top="1dp" android:left="1dp" android:right="1dp" android:bottom="3dp">
        <shape android:shape="rectangle">
            <solid android:color="@color/shadow_light" />
            <corners android:radius="16dp" />
        </shape>
    </item>

    <!-- 메인 카드 배경 -->
    <item android:bottom="2dp">
        <shape android:shape="rectangle">
            <solid android:color="@color/card_background" />
            <corners android:radius="16dp" />
            <stroke
                android:width="0.5dp"
                android:color="@color/border_light" />
        </shape>
    </item>

</layer-list>
