{"formatVersion": 1, "database": {"version": 6, "identityHash": "98fcc5f2d3d1bf53f965112064635173", "entities": [{"tableName": "user", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userId` TEXT NOT NULL, `nickname` TEXT, `profileImage` TEXT, `email` TEXT, `gender` TEXT, `phone` TEXT, `password` TEXT, `created_at` INTEGER NOT NULL, `is_active` INTEGER NOT NULL, PRIMARY KEY(`userId`))", "fields": [{"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "nickname", "columnName": "nickname", "affinity": "TEXT", "notNull": false}, {"fieldPath": "profileImage", "columnName": "profileImage", "affinity": "TEXT", "notNull": false}, {"fieldPath": "email", "columnName": "email", "affinity": "TEXT", "notNull": false}, {"fieldPath": "gender", "columnName": "gender", "affinity": "TEXT", "notNull": false}, {"fieldPath": "phone", "columnName": "phone", "affinity": "TEXT", "notNull": false}, {"fieldPath": "password", "columnName": "password", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createdAt", "columnName": "created_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isActive", "columnName": "is_active", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["userId"]}, "indices": [], "foreignKeys": []}, {"tableName": "friend", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`user_id` TEXT NOT NULL, `friend_id` TEXT NOT NULL, `friend_nickname` TEXT, `added_at` INTEGER NOT NULL, `status` TEXT, PRIMARY KEY(`user_id`, `friend_id`))", "fields": [{"fieldPath": "userId", "columnName": "user_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "friendId", "columnName": "friend_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "friend<PERSON><PERSON><PERSON>", "columnName": "friend_nickname", "affinity": "TEXT", "notNull": false}, {"fieldPath": "addedAt", "columnName": "added_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["user_id", "friend_id"]}, "indices": [], "foreignKeys": []}, {"tableName": "schedule", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `title` TEXT, `date_time` TEXT, `departure` TEXT, `destination` TEXT, `memo` TEXT, `user_id` TEXT, `is_shared` INTEGER NOT NULL, `created_at` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": false}, {"fieldPath": "dateTime", "columnName": "date_time", "affinity": "TEXT", "notNull": false}, {"fieldPath": "departure", "columnName": "departure", "affinity": "TEXT", "notNull": false}, {"fieldPath": "destination", "columnName": "destination", "affinity": "TEXT", "notNull": false}, {"fieldPath": "memo", "columnName": "memo", "affinity": "TEXT", "notNull": false}, {"fieldPath": "userId", "columnName": "user_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isShared", "columnName": "is_shared", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "created_at", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "Participant", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`scheduleId` INTEGER NOT NULL, `userId` TEXT NOT NULL, `isAccepted` INTEGER NOT NULL, PRIMARY KEY(`scheduleId`, `userId`))", "fields": [{"fieldPath": "scheduleId", "columnName": "scheduleId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isAccepted", "columnName": "isAccepted", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["scheduleId", "userId"]}, "indices": [], "foreignKeys": []}, {"tableName": "notification", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `type` TEXT, `title` TEXT, `message` TEXT, `fromUserId` TEXT, `scheduleId` TEXT, `status` TEXT, `timestamp` INTEGER NOT NULL, `isRead` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": false}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": false}, {"fieldPath": "message", "columnName": "message", "affinity": "TEXT", "notNull": false}, {"fieldPath": "fromUserId", "columnName": "fromUserId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "scheduleId", "columnName": "scheduleId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT", "notNull": false}, {"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isRead", "columnName": "isRead", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '98fcc5f2d3d1bf53f965112064635173')"]}}