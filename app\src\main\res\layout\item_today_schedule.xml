<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cardBackground"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    android:background="@drawable/card_normal">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 시간 정보 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginEnd="8dp"
                android:src="@drawable/ic_access_time"
                app:tint="@color/primary_color" />

            <TextView
                android:id="@+id/textTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="09:00"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="@color/primary_color" />

        </LinearLayout>

        <!-- 일정 제목 -->
        <TextView
            android:id="@+id/textTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="일정 제목"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            android:maxLines="2"
            android:ellipsize="end" />

        <!-- 위치 정보 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:layout_marginEnd="6dp"
                android:src="@drawable/ic_location_on"
                app:tint="@android:color/darker_gray" />

            <TextView
                android:id="@+id/textLocation"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="출발지 → 도착지"
                android:textSize="14sp"
                android:textColor="@android:color/darker_gray"
                android:maxLines="1"
                android:ellipsize="end" />

        </LinearLayout>

        <!-- 메모 (있는 경우만 표시) -->
        <TextView
            android:id="@+id/textMemo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="메모 내용"
            android:textSize="12sp"
            android:textColor="@android:color/darker_gray"
            android:maxLines="2"
            android:ellipsize="end"
            android:visibility="gone" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
