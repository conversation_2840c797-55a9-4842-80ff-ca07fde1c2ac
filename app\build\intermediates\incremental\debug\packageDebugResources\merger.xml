<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\TimeMate\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\TimeMate\app\src\main\res"><file name="fade_in" path="C:\Users\<USER>\TimeMate\app\src\main\res\anim\fade_in.xml" qualifiers="" type="anim"/><file name="fade_out" path="C:\Users\<USER>\TimeMate\app\src\main\res\anim\fade_out.xml" qualifiers="" type="anim"/><file name="card_elevation_animator" path="C:\Users\<USER>\TimeMate\app\src\main\res\animator\card_elevation_animator.xml" qualifiers="" type="animator"/><file name="bottom_nav_color" path="C:\Users\<USER>\TimeMate\app\src\main\res\color\bottom_nav_color.xml" qualifiers="" type="color"/><file name="badge_background" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\badge_background.xml" qualifiers="" type="drawable"/><file name="bottom_sheet_background" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\bottom_sheet_background.xml" qualifiers="" type="drawable"/><file name="button_accept" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\button_accept.xml" qualifiers="" type="drawable"/><file name="button_outline" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\button_outline.xml" qualifiers="" type="drawable"/><file name="button_primary" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\button_primary.xml" qualifiers="" type="drawable"/><file name="button_primary_ios" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\button_primary_ios.xml" qualifiers="" type="drawable"/><file name="button_reject" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\button_reject.xml" qualifiers="" type="drawable"/><file name="button_secondary_ios" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\button_secondary_ios.xml" qualifiers="" type="drawable"/><file name="card_completed" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\card_completed.xml" qualifiers="" type="drawable"/><file name="card_normal" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\card_normal.xml" qualifiers="" type="drawable"/><file name="card_overdue" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\card_overdue.xml" qualifiers="" type="drawable"/><file name="card_selected" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\card_selected.xml" qualifiers="" type="drawable"/><file name="category_background" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\category_background.xml" qualifiers="" type="drawable"/><file name="category_tag_background" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\category_tag_background.xml" qualifiers="" type="drawable"/><file name="circle_background" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\circle_background.xml" qualifiers="" type="drawable"/><file name="circle_background_ios" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\circle_background_ios.xml" qualifiers="" type="drawable"/><file name="circle_dot" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\circle_dot.xml" qualifiers="" type="drawable"/><file name="edit_text_background" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\edit_text_background.xml" qualifiers="" type="drawable"/><file name="ic_access_time" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_access_time.xml" qualifiers="" type="drawable"/><file name="ic_add" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_add.xml" qualifiers="" type="drawable"/><file name="ic_arrow_back" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_arrow_back.xml" qualifiers="" type="drawable"/><file name="ic_arrow_forward" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_arrow_forward.xml" qualifiers="" type="drawable"/><file name="ic_arrow_right" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_arrow_right.xml" qualifiers="" type="drawable"/><file name="ic_calendar" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_calendar.xml" qualifiers="" type="drawable"/><file name="ic_cancel" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_cancel.xml" qualifiers="" type="drawable"/><file name="ic_check" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_check.xml" qualifiers="" type="drawable"/><file name="ic_check_circle" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_check_circle.xml" qualifiers="" type="drawable"/><file name="ic_close" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_close.xml" qualifiers="" type="drawable"/><file name="ic_directions" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_directions.xml" qualifiers="" type="drawable"/><file name="ic_directions_car" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_directions_car.xml" qualifiers="" type="drawable"/><file name="ic_directions_transit" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_directions_transit.xml" qualifiers="" type="drawable"/><file name="ic_directions_walk" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_directions_walk.xml" qualifiers="" type="drawable"/><file name="ic_driving" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_driving.xml" qualifiers="" type="drawable"/><file name="ic_edit" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_edit.xml" qualifiers="" type="drawable"/><file name="ic_friends" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_friends.xml" qualifiers="" type="drawable"/><file name="ic_home" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_home.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_location" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_location.xml" qualifiers="" type="drawable"/><file name="ic_location_end" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_location_end.xml" qualifiers="" type="drawable"/><file name="ic_location_on" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_location_on.xml" qualifiers="" type="drawable"/><file name="ic_location_start" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_location_start.xml" qualifiers="" type="drawable"/><file name="ic_map_error" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_map_error.xml" qualifiers="" type="drawable"/><file name="ic_map_placeholder" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_map_placeholder.xml" qualifiers="" type="drawable"/><file name="ic_notifications" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_notifications.xml" qualifiers="" type="drawable"/><file name="ic_person" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_person.xml" qualifiers="" type="drawable"/><file name="ic_profile" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_profile.xml" qualifiers="" type="drawable"/><file name="ic_route_bus" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_route_bus.xml" qualifiers="" type="drawable"/><file name="ic_route_car" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_route_car.xml" qualifiers="" type="drawable"/><file name="ic_route_walk" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_route_walk.xml" qualifiers="" type="drawable"/><file name="ic_schedule" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_schedule.xml" qualifiers="" type="drawable"/><file name="ic_schedule_notification" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_schedule_notification.xml" qualifiers="" type="drawable"/><file name="ic_snooze" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_snooze.xml" qualifiers="" type="drawable"/><file name="ic_time" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_time.xml" qualifiers="" type="drawable"/><file name="ic_transit" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_transit.xml" qualifiers="" type="drawable"/><file name="ic_walking" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_walking.xml" qualifiers="" type="drawable"/><file name="ic_weather" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_weather.xml" qualifiers="" type="drawable"/><file name="indicator_dot_active" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\indicator_dot_active.xml" qualifiers="" type="drawable"/><file name="indicator_dot_inactive" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\indicator_dot_inactive.xml" qualifiers="" type="drawable"/><file name="ios_badge_background" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ios_badge_background.xml" qualifiers="" type="drawable"/><file name="ios_button_background" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ios_button_background.xml" qualifiers="" type="drawable"/><file name="ios_card_background" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ios_card_background.xml" qualifiers="" type="drawable"/><file name="modal_handle" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\modal_handle.xml" qualifiers="" type="drawable"/><file name="recommended_badge" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\recommended_badge.xml" qualifiers="" type="drawable"/><file name="route_normal_background" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\route_normal_background.xml" qualifiers="" type="drawable"/><file name="route_recommended_background" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\route_recommended_background.xml" qualifiers="" type="drawable"/><file name="schedule_info_background" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\schedule_info_background.xml" qualifiers="" type="drawable"/><file name="status_badge_background" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\status_badge_background.xml" qualifiers="" type="drawable"/><file name="timemate_button_background" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\timemate_button_background.xml" qualifiers="" type="drawable"/><file name="timemate_button_outlined" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\timemate_button_outlined.xml" qualifiers="" type="drawable"/><file name="timemate_button_primary" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\timemate_button_primary.xml" qualifiers="" type="drawable"/><file name="timemate_edittext_background" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\timemate_edittext_background.xml" qualifiers="" type="drawable"/><file name="weather_card_background" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\weather_card_background.xml" qualifiers="" type="drawable"/><file name="activity_account_switch" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_account_switch.xml" qualifiers="" type="layout"/><file name="activity_friend_add" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_friend_add.xml" qualifiers="" type="layout"/><file name="activity_friend_list" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_friend_list.xml" qualifiers="" type="layout"/><file name="activity_home" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_home.xml" qualifiers="" type="layout"/><file name="activity_main" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_manual_login" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_manual_login.xml" qualifiers="" type="layout"/><file name="activity_notification" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_notification.xml" qualifiers="" type="layout"/><file name="activity_notifications" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_notifications.xml" qualifiers="" type="layout"/><file name="activity_password_reset" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_password_reset.xml" qualifiers="" type="layout"/><file name="activity_profile" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_profile.xml" qualifiers="" type="layout"/><file name="activity_recommendation" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_recommendation.xml" qualifiers="" type="layout"/><file name="activity_schedule_add" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_add.xml" qualifiers="" type="layout"/><file name="activity_schedule_calendar" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_calendar.xml" qualifiers="" type="layout"/><file name="activity_schedule_list" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_list.xml" qualifiers="" type="layout"/><file name="activity_schedule_reminder_detail" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_reminder_detail.xml" qualifiers="" type="layout"/><file name="activity_signup_form" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_signup_form.xml" qualifiers="" type="layout"/><file name="dialog_directions_bottom_sheet" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_directions_bottom_sheet.xml" qualifiers="" type="layout"/><file name="dialog_friend_selection" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_friend_selection.xml" qualifiers="" type="layout"/><file name="dialog_route_options" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml" qualifiers="" type="layout"/><file name="dialog_schedule_detail" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail.xml" qualifiers="" type="layout"/><file name="dialog_schedule_detail_improved" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_improved.xml" qualifiers="" type="layout"/><file name="item_account" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_account.xml" qualifiers="" type="layout"/><file name="item_friend" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_friend.xml" qualifiers="" type="layout"/><file name="item_friend_selection" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_friend_selection.xml" qualifiers="" type="layout"/><file name="item_home_schedule" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_home_schedule.xml" qualifiers="" type="layout"/><file name="item_notification" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_notification.xml" qualifiers="" type="layout"/><file name="item_place_autocomplete" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_place_autocomplete.xml" qualifiers="" type="layout"/><file name="item_place_suggest" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_place_suggest.xml" qualifiers="" type="layout"/><file name="item_place_suggestion" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_place_suggestion.xml" qualifiers="" type="layout"/><file name="item_recommendation" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_recommendation.xml" qualifiers="" type="layout"/><file name="item_route_card" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_route_card.xml" qualifiers="" type="layout"/><file name="item_route_option" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_route_option.xml" qualifiers="" type="layout"/><file name="item_schedule" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule.xml" qualifiers="" type="layout"/><file name="item_schedule_detail" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_detail.xml" qualifiers="" type="layout"/><file name="item_schedule_header" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_header.xml" qualifiers="" type="layout"/><file name="item_schedule_improved" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_improved.xml" qualifiers="" type="layout"/><file name="item_schedule_list" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_list.xml" qualifiers="" type="layout"/><file name="item_today_schedule" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_today_schedule.xml" qualifiers="" type="layout"/><file name="item_tomorrow_reminder_card" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_tomorrow_reminder_card.xml" qualifiers="" type="layout"/><file name="item_tomorrow_reminder_header" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_tomorrow_reminder_header.xml" qualifiers="" type="layout"/><file name="bottom_nav_menu" path="C:\Users\<USER>\TimeMate\app\src\main\res\menu\bottom_nav_menu.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="C:\Users\<USER>\TimeMate\app\src\main\res\mipmap-anydpi\ic_launcher.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\TimeMate\app\src\main\res\mipmap-anydpi\ic_launcher_round.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\TimeMate\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\TimeMate\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\TimeMate\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\TimeMate\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\TimeMate\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\TimeMate\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\TimeMate\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\TimeMate\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\TimeMate\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\TimeMate\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\TimeMate\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="primary_color">#B5DFFF</color><color name="sky_blue">#B5DFFF</color><color name="sky_blue_primary">#B5DFFF</color><color name="sky_blue_light">#E8F6FD</color><color name="sky_blue_dark">#007AFF</color><color name="sky_blue_accent">#5BB6D6</color><color name="ios_blue">#007AFF</color><color name="ios_blue_dark">#0056CC</color><color name="ios_green">#34C759</color><color name="ios_orange">#FF9500</color><color name="ios_red">#FF3B30</color><color name="ios_purple">#AF52DE</color><color name="ios_pink">#FF2D92</color><color name="ios_yellow">#FFCC00</color><color name="pastel_blue">#E3F2FD</color><color name="pastel_pink">#FFE4E6</color><color name="pastel_mint">#E8F8F5</color><color name="pastel_yellow">#FFF8DC</color><color name="pastel_lavender">#F0F0FF</color><color name="pastel_peach">#FFEEE6</color><color name="pastel_sky">#E8F6FD</color><color name="dark_gray">#666666</color><color name="light_gray">#E0E0E0</color><color name="gray">#999999</color><color name="primary">#B5DFFF</color><color name="accent">#007AFF</color><color name="background">#FFFFFF</color><color name="surface">#FFFFFF</color><color name="card_background">#FFFFFF</color><color name="card_selected">#33B5DFFF</color><color name="card_pressed">#4DB5DFFF</color><color name="card_stroke">#B5DFFF</color><color name="text_primary">#1C1C1E</color><color name="text_secondary">#3A3A3C</color><color name="text_tertiary">#8E8E93</color><color name="text_hint">#C7C7CC</color><color name="text_on_primary">#FFFFFF</color><color name="text_input">#000000</color><color name="text_input_hint">#8E8E93</color><color name="success">#34C759</color><color name="warning">#FF9500</color><color name="error">#FF3B30</color><color name="info">#007AFF</color><color name="modal_background">#F2F2F7</color><color name="modal_handle">#C7C7CC</color><color name="route_card_background">@color/card_background</color><color name="route_card_selected">@color/card_selected</color><color name="route_text_primary">@color/text_primary</color><color name="route_text_secondary">@color/text_secondary</color><color name="route_accent">@color/info</color><color name="route_success">@color/success</color><color name="route_warning">@color/warning</color><color name="background_primary">#FFFFFF</color><color name="background_secondary">#F2F2F7</color><color name="background_tertiary">#FFFFFF</color><color name="background_light">#F8F9FA</color><color name="purple_500">#AF52DE</color><color name="purple_700">#8E44AD</color><color name="green">#34C759</color><color name="red">#FF3B30</color><color name="orange">#FF9500</color><color name="shadow_light">#1A000000</color><color name="divider">#E5E5EA</color><color name="border_light">#F2F2F7</color><color name="ios_gray3">#C7C7CC</color><color name="ios_gray6">#F2F2F7</color><color name="ios_gray5">#E5E5EA</color><color name="ios_blue_light">#B5DFFF</color><color name="background_card">#FFFFFF</color><color name="ios_red_light">#FFE4E6</color><color name="ios_green_light">#E8F8F5</color><color name="ios_purple_light">#F0F0FF</color><color name="ios_orange_light">#FFF8DC</color></file><file path="C:\Users\<USER>\TimeMate\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="card_corner_radius">20dp</dimen><dimen name="card_elevation">2dp</dimen><dimen name="card_elevation_selected">4dp</dimen><dimen name="card_elevation_pressed">6dp</dimen><dimen name="card_margin">12dp</dimen><dimen name="card_padding">16dp</dimen><dimen name="card_spacing">8dp</dimen><dimen name="card_height">72dp</dimen><dimen name="route_card_corner_radius">@dimen/card_corner_radius</dimen><dimen name="route_card_elevation">@dimen/card_elevation</dimen><dimen name="route_card_margin">@dimen/card_margin</dimen><dimen name="route_card_padding">@dimen/card_padding</dimen><dimen name="route_card_spacing">@dimen/card_spacing</dimen><dimen name="route_title_text_size">18sp</dimen><dimen name="route_subtitle_text_size">14sp</dimen><dimen name="route_detail_text_size">12sp</dimen><dimen name="route_time_text_size">16sp</dimen><dimen name="route_icon_size">24dp</dimen><dimen name="route_icon_small">20dp</dimen><dimen name="route_checkbox_size">20dp</dimen><dimen name="modal_corner_radius">16dp</dimen><dimen name="modal_handle_width">36dp</dimen><dimen name="modal_handle_height">4dp</dimen><dimen name="modal_handle_margin">8dp</dimen><dimen name="route_section_spacing">16dp</dimen><dimen name="route_item_spacing">12dp</dimen><dimen name="route_content_spacing">8dp</dimen><dimen name="route_button_height">48dp</dimen><dimen name="route_button_corner_radius">12dp</dimen></file><file path="C:\Users\<USER>\TimeMate\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">TimeMate</string></file><file path="C:\Users\<USER>\TimeMate\app\src\main\res\values\styles.xml" qualifiers=""><style name="TimeMateTheme" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="colorPrimary">@color/sky_blue_primary</item>
        <item name="colorPrimaryVariant">@color/sky_blue_dark</item>
        <item name="colorOnPrimary">@color/text_primary</item>
        <item name="colorSecondary">@color/sky_blue_accent</item>
        <item name="colorSecondaryVariant">@color/sky_blue_dark</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:colorBackground">@color/background_secondary</item>
        <item name="colorOnBackground">@color/text_primary</item>
        <item name="colorSurface">@color/card_background</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <item name="android:statusBarColor">@color/background_secondary</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:fontFamily">@font/pretendard_regular</item>
    </style><style name="TimeMateButton">
        <item name="android:textSize">17sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:fontFamily">@font/pretendard_medium</item>
        <item name="android:paddingTop">14dp</item>
        <item name="android:paddingBottom">14dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:elevation">1dp</item>
        <item name="android:background">@drawable/timemate_button_background</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:letterSpacing">-0.02</item>
        <item name="android:includeFontPadding">false</item>
    </style><style name="TimeMateButtonPrimary">
        <item name="android:textSize">17sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:fontFamily">@font/pretendard_bold</item>
        <item name="android:paddingTop">14dp</item>
        <item name="android:paddingBottom">14dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:background">@drawable/timemate_button_primary</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:elevation">1dp</item>
        <item name="android:letterSpacing">-0.02</item>
        <item name="android:includeFontPadding">false</item>
    </style><style name="TimeMateButtonOutlined">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:paddingTop">14dp</item>
        <item name="android:paddingBottom">14dp</item>
        <item name="android:background">@drawable/timemate_button_outlined</item>
        <item name="android:textColor">@color/sky_blue_accent</item>
    </style><style name="TimeMateCard">
        <item name="android:background">@drawable/ios_card_background</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:elevation">4dp</item>
    </style><style name="TimeMateTextTitle">
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style><style name="TimeMateTextSubtitle">
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style><style name="TimeMateTextBody">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style><style name="TimeMateTextCaption">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">@color/text_tertiary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style><style name="TimeMateTextTemperature">
        <item name="android:textSize">48sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif-light</item>
    </style><style name="TimeMateEditText">
        <item name="android:background">@drawable/timemate_edittext_background</item>
        <item name="android:textColorHint">@color/text_hint</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:padding">12dp</item>
    </style><style name="TimeMateBottomNavigation">
        <item name="android:background">@color/card_background</item>
        <item name="android:elevation">8dp</item>
    </style><style name="TimeMateAppBar">
        <item name="android:background">@color/sky_blue_primary</item>
        <item name="android:elevation">0dp</item>
        <item name="android:paddingTop">16dp</item>
        <item name="android:paddingBottom">16dp</item>
        <item name="android:paddingStart">20dp</item>
        <item name="android:paddingEnd">20dp</item>
    </style><style name="CalendarWeekDayTextAppearance">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_secondary</item>
    </style><style name="CalendarDateTextAppearance">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">@color/text_primary</item>
    </style><style name="CalendarHeaderTextAppearance">
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
    </style><style name="BottomSheetDialogTheme" parent="Theme.Material3.DayNight.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/BottomSheetStyle</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
    </style><style name="BottomSheetStyle" parent="Widget.Material3.BottomSheet">
        <item name="shapeAppearanceOverlay">@style/BottomSheetShapeAppearance</item>
        <item name="behavior_peekHeight">400dp</item>
        <item name="behavior_hideable">true</item>
        <item name="behavior_skipCollapsed">false</item>
        <item name="android:elevation">8dp</item>
    </style><style name="BottomSheetShapeAppearance" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopLeft">@dimen/modal_corner_radius</item>
        <item name="cornerSizeTopRight">@dimen/modal_corner_radius</item>
        <item name="cornerSizeBottomLeft">0dp</item>
        <item name="cornerSizeBottomRight">0dp</item>
    </style><style name="iOSTitle">
        <item name="android:textSize">28sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">@font/pretendard_bold</item>
        <item name="android:letterSpacing">-0.03</item>
        <item name="android:includeFontPadding">false</item>
    </style><style name="iOSHeadline">
        <item name="android:textSize">22sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">@font/pretendard_bold</item>
        <item name="android:letterSpacing">-0.02</item>
        <item name="android:includeFontPadding">false</item>
    </style><style name="iOSBody">
        <item name="android:textSize">17sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">@font/pretendard_regular</item>
        <item name="android:letterSpacing">-0.02</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:lineSpacingExtra">2dp</item>
    </style><style name="iOSFootnote">
        <item name="android:textSize">13sp</item>
        <item name="android:textColor">@color/text_tertiary</item>
        <item name="android:fontFamily">@font/pretendard_regular</item>
        <item name="android:letterSpacing">-0.01</item>
        <item name="android:includeFontPadding">false</item>
    </style><style name="iOSSubheadline">
        <item name="android:textSize">17sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">@font/pretendard_medium</item>
        <item name="android:letterSpacing">-0.02</item>
        <item name="android:includeFontPadding">false</item>
    </style><style name="iOSCallout">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:fontFamily">@font/pretendard_regular</item>
        <item name="android:letterSpacing">-0.01</item>
        <item name="android:includeFontPadding">false</item>
    </style><style name="iOSEditText">
        <item name="android:layout_height">50dp</item>
        <item name="android:background">@drawable/timemate_edittext_background</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:textSize">17sp</item>
        <item name="android:textColor">@color/text_input</item>
        <item name="android:textColorHint">@color/text_input_hint</item>
        <item name="android:fontFamily">@font/pretendard_regular</item>
        <item name="android:letterSpacing">-0.02</item>
        <item name="android:includeFontPadding">false</item>
    </style><style name="iOSCaption">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/text_tertiary</item>
        <item name="android:fontFamily">@font/pretendard_regular</item>
        <item name="android:letterSpacing">0</item>
        <item name="android:includeFontPadding">false</item>
    </style></file><file path="C:\Users\<USER>\TimeMate\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.TimeMate" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style><style name="Theme.TimeMate" parent="Base.Theme.TimeMate"/></file><file path="C:\Users\<USER>\TimeMate\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.TimeMate" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style></file><file name="backup_rules" path="C:\Users\<USER>\TimeMate\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\TimeMate\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="dialog_schedule_detail_ios" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_ios.xml" qualifiers="" type="layout"/><file name="bg_tag_rounded" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\bg_tag_rounded.xml" qualifiers="" type="drawable"/><file name="item_ootd_recommendation" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_ootd_recommendation.xml" qualifiers="" type="layout"/><file name="pretendard_bold" path="C:\Users\<USER>\TimeMate\app\src\main\res\font\pretendard_bold.xml" qualifiers="" type="font"/><file name="pretendard_medium" path="C:\Users\<USER>\TimeMate\app\src\main\res\font\pretendard_medium.xml" qualifiers="" type="font"/><file name="pretendard_regular" path="C:\Users\<USER>\TimeMate\app\src\main\res\font\pretendard_regular.xml" qualifiers="" type="font"/><file name="ic_image_error" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_image_error.xml" qualifiers="" type="drawable"/><file name="ic_image_placeholder" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_image_placeholder.xml" qualifiers="" type="drawable"/><file name="ios_chip_background" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ios_chip_background.xml" qualifiers="" type="drawable"/><file name="item_place_with_image" path="C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_place_with_image.xml" qualifiers="" type="layout"/><file name="ic_phone" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_phone.xml" qualifiers="" type="drawable"/><file name="ios_category_text_selector" path="C:\Users\<USER>\TimeMate\app\src\main\res\color\ios_category_text_selector.xml" qualifiers="" type="color"/><file name="ios_category_button_selector" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ios_category_button_selector.xml" qualifiers="" type="drawable"/><file name="ios_count_background" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ios_count_background.xml" qualifiers="" type="drawable"/><file name="ios_distance_background" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ios_distance_background.xml" qualifiers="" type="drawable"/><file name="ios_icon_background" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ios_icon_background.xml" qualifiers="" type="drawable"/><file name="ios_image_placeholder" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ios_image_placeholder.xml" qualifiers="" type="drawable"/><file name="ios_navigation_button" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ios_navigation_button.xml" qualifiers="" type="drawable"/><file name="ios_rating_background" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ios_rating_background.xml" qualifiers="" type="drawable"/><file name="ios_search_button" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ios_search_button.xml" qualifiers="" type="drawable"/><file name="ios_circle_button_white" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ios_circle_button_white.xml" qualifiers="" type="drawable"/><file name="ios_header_blue_gradient" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ios_header_blue_gradient.xml" qualifiers="" type="drawable"/><file name="notification_badge_background" path="C:\Users\<USER>\TimeMate\app\src\main\res\drawable\notification_badge_background.xml" qualifiers="" type="drawable"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\TimeMate\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\TimeMate\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\TimeMate\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\TimeMate\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>