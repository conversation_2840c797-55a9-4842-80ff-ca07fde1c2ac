<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 눌렸을 때 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/ios_blue_dark" />
            <corners android:radius="12dp" />
        </shape>
    </item>
    
    <!-- 비활성화 상태 -->
    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <solid android:color="@color/text_hint" />
            <corners android:radius="12dp" />
        </shape>
    </item>
    
    <!-- 기본 상태 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/ios_blue" />
            <corners android:radius="12dp" />
        </shape>
    </item>
    
</selector>
