<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 선택된 상태 -->
    <item android:state_selected="true">
        <set>
            <objectAnimator
                android:propertyName="elevation"
                android:duration="150"
                android:valueTo="@dimen/card_elevation_selected"
                android:valueType="floatType" />
        </set>
    </item>
    
    <!-- 눌린 상태 -->
    <item android:state_pressed="true">
        <set>
            <objectAnimator
                android:propertyName="elevation"
                android:duration="100"
                android:valueTo="@dimen/card_elevation_pressed"
                android:valueType="floatType" />
        </set>
    </item>
    
    <!-- 기본 상태 -->
    <item>
        <set>
            <objectAnimator
                android:propertyName="elevation"
                android:duration="150"
                android:valueTo="@dimen/card_elevation"
                android:valueType="floatType" />
        </set>
    </item>
    
</selector>
