<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_primary">

    <!-- iOS 스타일 헤더 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/ios_blue"
        android:orientation="horizontal"
        android:paddingHorizontal="20dp"
        android:paddingVertical="16dp"
        android:paddingTop="40dp"
        android:gravity="center_vertical"
        android:elevation="4dp">

        <ImageButton
            android:id="@+id/btnBack"
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:src="@drawable/ic_arrow_back"
            android:background="@drawable/ios_button_background"
            android:scaleType="centerInside"
            android:padding="12dp"
            app:tint="@android:color/white" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="알림"
            style="@style/iOSTitle"
            android:textColor="@android:color/white"
            android:gravity="center"
            android:layout_marginHorizontal="16dp" />

        <View
            android:layout_width="44dp"
            android:layout_height="44dp" />

    </LinearLayout>

    <!-- iOS 스타일 알림 목록 -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/background_primary">

        <!-- 알림 RecyclerView -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerNotifications"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="20dp"
            android:clipToPadding="false"
            android:scrollbars="none"
            android:overScrollMode="never" />

        <!-- iOS 스타일 빈 상태 -->
        <LinearLayout
            android:id="@+id/textEmptyNotifications"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="40dp"
            android:visibility="gone">

            <ImageView
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:src="@drawable/ic_notifications"
                app:tint="@color/ios_blue_light"
                android:layout_marginBottom="24dp"
                android:alpha="0.6" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="알림이 없습니다"
                style="@style/iOSHeadline"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="12dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="친구 초대나 일정 공유 요청이 있으면\n여기에 표시됩니다"
                style="@style/iOSCallout"
                android:textColor="@color/text_secondary"
                android:textAlignment="center"
                android:lineSpacingExtra="4dp" />

        </LinearLayout>

    </FrameLayout>

    <!-- iOS 스타일 바텀 네비게이션 -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottomNavigationView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        style="@style/TimeMateBottomNavigation"
        app:itemIconTint="@color/bottom_nav_color"
        app:itemTextColor="@color/bottom_nav_color"
        app:menu="@menu/bottom_nav_menu" />

</LinearLayout>
