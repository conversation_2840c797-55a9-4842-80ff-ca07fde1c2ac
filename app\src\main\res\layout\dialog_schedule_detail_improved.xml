<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="16dp"
    app:cardCornerRadius="20dp"
    app:cardElevation="8dp"
    app:cardBackgroundColor="@color/card_background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp">

        <!-- 헤더 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="20dp">

            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_schedule"
                android:background="@drawable/circle_background"
                android:padding="6dp"
                app:tint="@color/accent"
                android:layout_marginEnd="12dp" />

            <TextView
                android:id="@+id/textScheduleTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="일정 제목"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary" />

            <ImageButton
                android:id="@+id/btnCloseDialog"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_close"
                android:background="@drawable/circle_background"
                android:padding="6dp"
                app:tint="@color/text_secondary" />

        </LinearLayout>

        <!-- 일정 정보 섹션 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/timemate_edittext_background"
            android:padding="16dp"
            android:layout_marginBottom="16dp">

            <!-- 날짜 및 시간 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_calendar"
                    app:tint="@color/accent"
                    android:layout_marginEnd="12dp" />

                <TextView
                    android:id="@+id/textScheduleDateTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="2024년 1월 15일 14:30"
                    android:textSize="16sp"
                    android:textColor="@color/text_primary" />

            </LinearLayout>

            <!-- 출발지 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_location_start"
                    app:tint="@color/ios_green"
                    android:layout_marginEnd="12dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="출발지:"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_secondary"
                    android:layout_marginEnd="8dp" />

                <TextView
                    android:id="@+id/textScheduleDeparture"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="서울역"
                    android:textSize="16sp"
                    android:textColor="@color/text_primary" />

            </LinearLayout>

            <!-- 도착지 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_location_end"
                    app:tint="@color/ios_red"
                    android:layout_marginEnd="12dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="도착지:"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_secondary"
                    android:layout_marginEnd="8dp" />

                <TextView
                    android:id="@+id/textScheduleDestination"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="강남역"
                    android:textSize="16sp"
                    android:textColor="@color/text_primary" />

            </LinearLayout>

            <!-- 메모 -->
            <LinearLayout
                android:id="@+id/layoutMemo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="top"
                android:visibility="gone">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_edit"
                    app:tint="@color/accent"
                    android:layout_marginEnd="12dp"
                    android:layout_marginTop="2dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="메모:"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_secondary"
                    android:layout_marginEnd="8dp" />

                <TextView
                    android:id="@+id/textScheduleMemo"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="중요한 회의입니다"
                    android:textSize="16sp"
                    android:textColor="@color/text_primary" />

            </LinearLayout>

        </LinearLayout>

        <!-- 액션 버튼들 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <Button
                android:id="@+id/btnEditSchedule"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:text="✏️ 수정"
                android:textSize="14sp"
                android:textStyle="bold"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_marginEnd="8dp" />

            <Button
                android:id="@+id/btnDeleteSchedule"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:text="🗑️ 삭제"
                android:textSize="14sp"
                android:textStyle="bold"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:textColor="@color/ios_red"
                android:strokeColor="@color/ios_red"
                android:layout_marginStart="8dp" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
