<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="16dp"
    android:layout_marginVertical="8dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="4dp"
    android:clickable="true"
    android:focusable="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/card_background"
        android:orientation="vertical"
        android:padding="20dp">

        <!-- 헤더 영역 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

            <!-- 일정 아이콘 -->
            <LinearLayout
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="@color/pastel_lavender"
                android:gravity="center"
                android:layout_marginEnd="16dp">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_calendar"
                    app:tint="@color/text_primary" />

            </LinearLayout>

            <!-- 제목과 시간 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/textTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="일정 제목"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/textDateTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="2024년 1월 1일 10:00"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary" />

            </LinearLayout>

            <!-- 공유 상태 표시 -->
            <ImageView
                android:id="@+id/iconShared"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_friends"
                app:tint="@color/sky_blue_accent"
                android:visibility="gone" />

        </LinearLayout>

        <!-- 위치 정보 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_location_start"
                app:tint="@color/green"
                android:layout_marginEnd="8dp" />

            <TextView
                android:id="@+id/textDeparture"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="출발지"
                android:textSize="14sp"
                android:textColor="@color/text_primary" />

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_arrow_forward"
                app:tint="@color/text_hint"
                android:layout_marginHorizontal="8dp" />

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_location_end"
                app:tint="@color/red"
                android:layout_marginEnd="8dp" />

            <TextView
                android:id="@+id/textDestination"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="도착지"
                android:textSize="14sp"
                android:textColor="@color/text_primary" />

        </LinearLayout>

        <!-- 메모 (있는 경우만 표시) -->
        <TextView
            android:id="@+id/textMemo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="메모 내용"
            android:textSize="14sp"
            android:textColor="@color/text_secondary"
            android:background="@color/pastel_yellow"
            android:padding="8dp"
            android:layout_marginBottom="12dp"
            android:visibility="gone" />

        <!-- 하단 액션 영역 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- 친구 정보 -->
            <TextView
                android:id="@+id/textFriends"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="개인 일정"
                android:textSize="12sp"
                android:textColor="@color/text_hint"
                android:drawableStart="@drawable/ic_person"
                android:drawablePadding="4dp"
                app:drawableTint="@color/text_hint" />

            <!-- 길찾기 버튼 -->
            <Button
                android:id="@+id/btnDirections"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:text="길찾기"
                android:textSize="12sp"
                android:backgroundTint="@color/sky_blue_accent"
                android:layout_marginStart="8dp"
                style="@style/Widget.Material3.Button" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
