<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="20dp"
    app:cardCornerRadius="24dp"
    app:cardElevation="12dp"
    app:cardBackgroundColor="@android:color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp">

        <!-- 헤더 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginBottom="24dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="👥 함께할 친구"
                android:textSize="22sp"
                android:textStyle="bold"
                android:fontFamily="sans-serif-medium"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="일정에 초대할 친구들을 선택하세요"
                android:textSize="14sp"
                android:fontFamily="sans-serif"
                android:textColor="@color/text_secondary"
                android:gravity="center" />

        </LinearLayout>

        <!-- 친구 목록 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerViewFriends"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:maxHeight="300dp"
            android:scrollbars="vertical" />

        <!-- 선택된 친구 수 표시 -->
        <TextView
            android:id="@+id/textSelectedCount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="0명 선택됨"
            android:textSize="14sp"
            android:fontFamily="sans-serif-medium"
            android:textColor="@color/accent"
            android:gravity="center"
            android:layout_marginBottom="20dp" />

        <!-- 액션 버튼들 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <Button
                android:id="@+id/btnCancel"
                android:layout_width="0dp"
                android:layout_height="52dp"
                android:layout_weight="1"
                android:text="취소"
                android:textSize="16sp"
                android:fontFamily="sans-serif-medium"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_marginEnd="8dp"
                app:cornerRadius="12dp" />

            <Button
                android:id="@+id/btnConfirm"
                android:layout_width="0dp"
                android:layout_height="52dp"
                android:layout_weight="1"
                android:text="확인"
                android:textSize="16sp"
                android:textStyle="bold"
                android:fontFamily="sans-serif-medium"
                style="@style/TimeMateButton"
                android:layout_marginStart="8dp"
                app:cornerRadius="12dp" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
