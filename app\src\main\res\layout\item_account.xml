<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    android:clickable="true"
    android:focusable="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <!-- 계정 아이콘 -->
        <LinearLayout
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:background="@color/pastel_lavender"
            android:gravity="center"
            android:layout_marginEnd="16dp">

            <ImageView
                android:id="@+id/iconAccount"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_person"
                app:tint="@color/text_primary" />

        </LinearLayout>

        <!-- 계정 정보 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/textNickname"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="사용자 닉네임"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="4dp" />

            <TextView
                android:id="@+id/textUserId"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="ID: user123"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:layout_marginBottom="2dp" />

            <TextView
                android:id="@+id/textEmail"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="<EMAIL>"
                android:textSize="12sp"
                android:textColor="@color/text_hint"
                android:layout_marginBottom="2dp" />

            <TextView
                android:id="@+id/textCreatedDate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="가입일: 2024.01.01"
                android:textSize="12sp"
                android:textColor="@color/text_hint" />

        </LinearLayout>

        <!-- 전환 화살표 -->
        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_arrow_forward"
            app:tint="@color/text_hint" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
