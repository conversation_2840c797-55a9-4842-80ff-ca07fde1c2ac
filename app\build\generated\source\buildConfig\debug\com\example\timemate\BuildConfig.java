/**
 * Automatically generated file. DO NOT MODIFY
 */
package com.example.timemate;

public final class BuildConfig {
  public static final boolean DEBUG = Boolean.parseBoolean("true");
  public static final String APPLICATION_ID = "com.example.timemate";
  public static final String BUILD_TYPE = "debug";
  public static final int VERSION_CODE = 1;
  public static final String VERSION_NAME = "1.0";
  // Field from default config.
  public static final String NAVER_CLOUD_CLIENT_ID = "dnnydofmgg";
  // Field from default config.
  public static final String NAVER_CLOUD_CLIENT_SECRET = "GlevAwH7wuE5x2zfXuzwL9KVcHJUq5p7P7zYSF45";
  // Field from default config.
  public static final String NAVER_DEV_CLIENT_ID = "e8_dH6tsAFlw80xK1aZn";
  // Field from default config.
  public static final String NAVER_DEV_CLIENT_SECRET = "zc3tVsHoTL";
  // Field from default config.
  public static final String NAVER_DIRECTIONS_URL = "https://maps.apigw.ntruss.com/map-direction/v1";
  // Field from default config.
  public static final String NAVER_GEOCODING_URL = "https://maps.apigw.ntruss.com/map-geocode/v2";
  // Field from default config.
  public static final String NAVER_LOCAL_SEARCH_URL = "https://openapi.naver.com/v1/search/local.json";
  // Field from default config.
  public static final String NAVER_REVERSE_GEOCODING_URL = "https://maps.apigw.ntruss.com/map-reversegeocode/v2";
  // Field from default config.
  public static final String NAVER_STATIC_MAP_URL = "https://maps.apigw.ntruss.com/map-static/v2";
  // Field from default config.
  public static final String OPENWEATHER_API_KEY = "********************************";
}
