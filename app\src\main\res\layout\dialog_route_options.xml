<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="20dp"
    app:cardCornerRadius="24dp"
    app:cardElevation="12dp"
    app:cardBackgroundColor="@android:color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp">

        <!-- 헤더 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginBottom="24dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="🗺️ 경로 선택"
                android:textSize="22sp"
                android:textStyle="bold"
                android:fontFamily="sans-serif-medium"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/textRouteTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="출발지 → 도착지"
                android:textSize="16sp"
                android:fontFamily="sans-serif"
                android:textColor="@color/text_secondary"
                android:gravity="center" />

        </LinearLayout>

        <!-- 스크롤 가능한 경로 옵션 컨테이너 -->
        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:maxHeight="300dp"
            android:scrollbars="vertical"
            android:fadeScrollbars="false"
            android:fillViewport="false">

            <!-- 경로 옵션 체크박스 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="20dp">

            <!-- 대중교통 옵션 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@android:color/white"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:minHeight="48dp">

                        <CheckBox
                            android:id="@+id/checkboxPublicTransport"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:buttonTint="@color/accent"
                            android:scaleX="1.2"
                            android:scaleY="1.2" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="🚌"
                            android:textSize="24dp"
                            android:layout_marginStart="12dp"
                            android:layout_marginEnd="8dp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="대중교통"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:fontFamily="sans-serif-medium"
                            android:textColor="@color/text_primary" />

                        <TextView
                            android:id="@+id/textPublicRecommended"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="⭐ 추천"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            android:textColor="@android:color/white"
                            android:background="@drawable/badge_background"
                            android:paddingHorizontal="8dp"
                            android:paddingVertical="4dp"
                            android:visibility="gone" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="12dp"
                        android:layout_marginStart="48dp">

                        <TextView
                            android:id="@+id/textPublicRoute"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="지하철/버스 (환승 포함)"
                            android:textSize="14sp"
                            android:fontFamily="sans-serif"
                            android:textColor="@color/text_secondary"
                            android:lineSpacingExtra="2dp" />

                        <TextView
                            android:id="@+id/textPublicTime"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="22분 (1,600원)"
                            android:textSize="15sp"
                            android:textStyle="bold"
                            android:fontFamily="sans-serif-medium"
                            android:textColor="@color/text_primary"
                            android:layout_marginStart="12dp" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 자동차 옵션 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@android:color/white"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:minHeight="48dp">

                        <CheckBox
                            android:id="@+id/checkboxDriving"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:buttonTint="@color/accent"
                            android:scaleX="1.2"
                            android:scaleY="1.2" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="🚗"
                            android:textSize="24dp"
                            android:layout_marginStart="12dp"
                            android:layout_marginEnd="8dp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="자동차"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:fontFamily="sans-serif-medium"
                            android:textColor="@color/text_primary" />

                        <TextView
                            android:id="@+id/textDrivingRecommended"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="⭐ 추천"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            android:textColor="@android:color/white"
                            android:background="@drawable/badge_background"
                            android:paddingHorizontal="8dp"
                            android:paddingVertical="4dp"
                            android:visibility="gone" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="12dp"
                        android:layout_marginStart="48dp">

                        <TextView
                            android:id="@+id/textDrivingRoute"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="최단거리 경로"
                            android:textSize="14sp"
                            android:fontFamily="sans-serif"
                            android:textColor="@color/text_secondary"
                            android:lineSpacingExtra="2dp" />

                        <TextView
                            android:id="@+id/textDrivingTime"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="18분 (4,100원)"
                            android:textSize="15sp"
                            android:textStyle="bold"
                            android:fontFamily="sans-serif-medium"
                            android:textColor="@color/text_primary"
                            android:layout_marginStart="12dp" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 자전거 옵션 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@android:color/white"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:minHeight="48dp">

                        <CheckBox
                            android:id="@+id/checkboxBicycle"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:buttonTint="@color/accent"
                            android:scaleX="1.2"
                            android:scaleY="1.2" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="🚴"
                            android:textSize="24dp"
                            android:layout_marginStart="12dp"
                            android:layout_marginEnd="8dp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="자전거"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:fontFamily="sans-serif-medium"
                            android:textColor="@color/text_primary" />

                        <TextView
                            android:id="@+id/textBicycleRecommended"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="⭐ 추천"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            android:textColor="@android:color/white"
                            android:background="@drawable/badge_background"
                            android:paddingHorizontal="8dp"
                            android:paddingVertical="4dp"
                            android:visibility="gone" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="12dp"
                        android:layout_marginStart="48dp">

                        <TextView
                            android:id="@+id/textBicycleRoute"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="자전거 경로"
                            android:textSize="14sp"
                            android:fontFamily="sans-serif"
                            android:textColor="@color/text_secondary"
                            android:lineSpacingExtra="2dp" />

                        <TextView
                            android:id="@+id/textBicycleTime"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="25분 (무료)"
                            android:textSize="15sp"
                            android:textStyle="bold"
                            android:fontFamily="sans-serif-medium"
                            android:textColor="@color/text_primary"
                            android:layout_marginStart="12dp" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 도보 옵션 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@android:color/white"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:minHeight="48dp">

                        <CheckBox
                            android:id="@+id/checkboxWalking"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:buttonTint="@color/accent"
                            android:scaleX="1.2"
                            android:scaleY="1.2" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="🚶"
                            android:textSize="24dp"
                            android:layout_marginStart="12dp"
                            android:layout_marginEnd="8dp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="도보"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:fontFamily="sans-serif-medium"
                            android:textColor="@color/text_primary" />

                        <TextView
                            android:id="@+id/textWalkingRecommended"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="⭐ 추천"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            android:textColor="@android:color/white"
                            android:background="@drawable/badge_background"
                            android:paddingHorizontal="8dp"
                            android:paddingVertical="4dp"
                            android:visibility="gone" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="12dp"
                        android:layout_marginStart="48dp">

                        <TextView
                            android:id="@+id/textWalkingRoute"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="도보 경로"
                            android:textSize="14sp"
                            android:fontFamily="sans-serif"
                            android:textColor="@color/text_secondary"
                            android:lineSpacingExtra="2dp" />

                        <TextView
                            android:id="@+id/textWalkingTime"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="1시간 38분 (무료)"
                            android:textSize="15sp"
                            android:textStyle="bold"
                            android:fontFamily="sans-serif-medium"
                            android:textColor="@color/text_primary"
                            android:layout_marginStart="12dp" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 택시 옵션 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@android:color/white"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:minHeight="48dp">

                        <CheckBox
                            android:id="@+id/checkboxTaxi"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:buttonTint="@color/accent"
                            android:scaleX="1.2"
                            android:scaleY="1.2" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="🚕"
                            android:textSize="24dp"
                            android:layout_marginStart="12dp"
                            android:layout_marginEnd="8dp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="택시"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:fontFamily="sans-serif-medium"
                            android:textColor="@color/text_primary" />

                        <TextView
                            android:id="@+id/textTaxiRecommended"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="⭐ 추천"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            android:textColor="@android:color/white"
                            android:background="@drawable/badge_background"
                            android:paddingHorizontal="8dp"
                            android:paddingVertical="4dp"
                            android:visibility="gone" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="12dp"
                        android:layout_marginStart="48dp">

                        <TextView
                            android:id="@+id/textTaxiRoute"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="택시 경로"
                            android:textSize="14sp"
                            android:fontFamily="sans-serif"
                            android:textColor="@color/text_secondary"
                            android:lineSpacingExtra="2dp" />

                        <TextView
                            android:id="@+id/textTaxiTime"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="15분 (8,200원)"
                            android:textSize="15sp"
                            android:textStyle="bold"
                            android:fontFamily="sans-serif-medium"
                            android:textColor="@color/text_primary"
                            android:layout_marginStart="12dp" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

        </ScrollView>

        <!-- 액션 버튼들 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_marginTop="8dp">

            <Button
                android:id="@+id/btnCancel"
                android:layout_width="0dp"
                android:layout_height="52dp"
                android:layout_weight="1"
                android:text="취소"
                android:textSize="16sp"
                android:fontFamily="sans-serif-medium"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_marginEnd="8dp"
                app:cornerRadius="12dp" />

            <Button
                android:id="@+id/btnConfirm"
                android:layout_width="0dp"
                android:layout_height="52dp"
                android:layout_weight="1"
                android:text="선택"
                android:textSize="16sp"
                android:textStyle="bold"
                android:fontFamily="sans-serif-medium"
                style="@style/TimeMateButton"
                android:layout_marginStart="8dp"
                app:cornerRadius="12dp" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
