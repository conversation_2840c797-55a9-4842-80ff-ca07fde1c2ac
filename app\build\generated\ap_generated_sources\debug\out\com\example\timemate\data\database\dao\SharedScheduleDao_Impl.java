package com.example.timemate.data.database.dao;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.timemate.data.model.SharedSchedule;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class SharedScheduleDao_Impl implements SharedScheduleDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<SharedSchedule> __insertionAdapterOfSharedSchedule;

  private final EntityDeletionOrUpdateAdapter<SharedSchedule> __deletionAdapterOfSharedSchedule;

  private final EntityDeletionOrUpdateAdapter<SharedSchedule> __updateAdapterOfSharedSchedule;

  private final SharedSQLiteStatement __preparedStmtOfUpdateStatus;

  private final SharedSQLiteStatement __preparedStmtOfDeleteByScheduleId;

  private final SharedSQLiteStatement __preparedStmtOfDeleteByUserId;

  public SharedScheduleDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfSharedSchedule = new EntityInsertionAdapter<SharedSchedule>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR ABORT INTO `shared_schedules` (`id`,`originalScheduleId`,`creatorUserId`,`creatorNickname`,`invitedUserId`,`invitedNickname`,`title`,`date`,`time`,`departure`,`destination`,`memo`,`status`,`isNotificationSent`,`isNotificationRead`,`sharedScheduleId`,`isSyncEnabled`,`createdAt`,`updatedAt`,`respondedAt`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final SharedSchedule entity) {
        statement.bindLong(1, entity.id);
        statement.bindLong(2, entity.originalScheduleId);
        if (entity.creatorUserId == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.creatorUserId);
        }
        if (entity.creatorNickname == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.creatorNickname);
        }
        if (entity.invitedUserId == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.invitedUserId);
        }
        if (entity.invitedNickname == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.invitedNickname);
        }
        if (entity.title == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.title);
        }
        if (entity.date == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.date);
        }
        if (entity.time == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.time);
        }
        if (entity.departure == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.departure);
        }
        if (entity.destination == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.destination);
        }
        if (entity.memo == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.memo);
        }
        if (entity.status == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.status);
        }
        final int _tmp = entity.isNotificationSent ? 1 : 0;
        statement.bindLong(14, _tmp);
        final int _tmp_1 = entity.isNotificationRead ? 1 : 0;
        statement.bindLong(15, _tmp_1);
        statement.bindLong(16, entity.sharedScheduleId);
        final int _tmp_2 = entity.isSyncEnabled ? 1 : 0;
        statement.bindLong(17, _tmp_2);
        statement.bindLong(18, entity.createdAt);
        statement.bindLong(19, entity.updatedAt);
        statement.bindLong(20, entity.respondedAt);
      }
    };
    this.__deletionAdapterOfSharedSchedule = new EntityDeletionOrUpdateAdapter<SharedSchedule>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `shared_schedules` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final SharedSchedule entity) {
        statement.bindLong(1, entity.id);
      }
    };
    this.__updateAdapterOfSharedSchedule = new EntityDeletionOrUpdateAdapter<SharedSchedule>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `shared_schedules` SET `id` = ?,`originalScheduleId` = ?,`creatorUserId` = ?,`creatorNickname` = ?,`invitedUserId` = ?,`invitedNickname` = ?,`title` = ?,`date` = ?,`time` = ?,`departure` = ?,`destination` = ?,`memo` = ?,`status` = ?,`isNotificationSent` = ?,`isNotificationRead` = ?,`sharedScheduleId` = ?,`isSyncEnabled` = ?,`createdAt` = ?,`updatedAt` = ?,`respondedAt` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final SharedSchedule entity) {
        statement.bindLong(1, entity.id);
        statement.bindLong(2, entity.originalScheduleId);
        if (entity.creatorUserId == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.creatorUserId);
        }
        if (entity.creatorNickname == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.creatorNickname);
        }
        if (entity.invitedUserId == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.invitedUserId);
        }
        if (entity.invitedNickname == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.invitedNickname);
        }
        if (entity.title == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.title);
        }
        if (entity.date == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.date);
        }
        if (entity.time == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.time);
        }
        if (entity.departure == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.departure);
        }
        if (entity.destination == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.destination);
        }
        if (entity.memo == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.memo);
        }
        if (entity.status == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.status);
        }
        final int _tmp = entity.isNotificationSent ? 1 : 0;
        statement.bindLong(14, _tmp);
        final int _tmp_1 = entity.isNotificationRead ? 1 : 0;
        statement.bindLong(15, _tmp_1);
        statement.bindLong(16, entity.sharedScheduleId);
        final int _tmp_2 = entity.isSyncEnabled ? 1 : 0;
        statement.bindLong(17, _tmp_2);
        statement.bindLong(18, entity.createdAt);
        statement.bindLong(19, entity.updatedAt);
        statement.bindLong(20, entity.respondedAt);
        statement.bindLong(21, entity.id);
      }
    };
    this.__preparedStmtOfUpdateStatus = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE shared_schedules SET status = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteByScheduleId = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM shared_schedules WHERE originalScheduleId = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteByUserId = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM shared_schedules WHERE invitedUserId = ?";
        return _query;
      }
    };
  }

  @Override
  public long insert(final SharedSchedule sharedSchedule) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      final long _result = __insertionAdapterOfSharedSchedule.insertAndReturnId(sharedSchedule);
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public int delete(final SharedSchedule sharedSchedule) {
    __db.assertNotSuspendingTransaction();
    int _total = 0;
    __db.beginTransaction();
    try {
      _total += __deletionAdapterOfSharedSchedule.handle(sharedSchedule);
      __db.setTransactionSuccessful();
      return _total;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public int update(final SharedSchedule sharedSchedule) {
    __db.assertNotSuspendingTransaction();
    int _total = 0;
    __db.beginTransaction();
    try {
      _total += __updateAdapterOfSharedSchedule.handle(sharedSchedule);
      __db.setTransactionSuccessful();
      return _total;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public int updateStatus(final long id, final String status) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateStatus.acquire();
    int _argIndex = 1;
    if (status == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, status);
    }
    _argIndex = 2;
    _stmt.bindLong(_argIndex, id);
    try {
      __db.beginTransaction();
      try {
        final int _result = _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
        return _result;
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfUpdateStatus.release(_stmt);
    }
  }

  @Override
  public int deleteByScheduleId(final long scheduleId) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteByScheduleId.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, scheduleId);
    try {
      __db.beginTransaction();
      try {
        final int _result = _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
        return _result;
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfDeleteByScheduleId.release(_stmt);
    }
  }

  @Override
  public int deleteByUserId(final String userId) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteByUserId.acquire();
    int _argIndex = 1;
    if (userId == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, userId);
    }
    try {
      __db.beginTransaction();
      try {
        final int _result = _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
        return _result;
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfDeleteByUserId.release(_stmt);
    }
  }

  @Override
  public SharedSchedule getSharedScheduleById(final long id) {
    final String _sql = "SELECT * FROM shared_schedules WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfOriginalScheduleId = CursorUtil.getColumnIndexOrThrow(_cursor, "originalScheduleId");
      final int _cursorIndexOfCreatorUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "creatorUserId");
      final int _cursorIndexOfCreatorNickname = CursorUtil.getColumnIndexOrThrow(_cursor, "creatorNickname");
      final int _cursorIndexOfInvitedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "invitedUserId");
      final int _cursorIndexOfInvitedNickname = CursorUtil.getColumnIndexOrThrow(_cursor, "invitedNickname");
      final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfTime = CursorUtil.getColumnIndexOrThrow(_cursor, "time");
      final int _cursorIndexOfDeparture = CursorUtil.getColumnIndexOrThrow(_cursor, "departure");
      final int _cursorIndexOfDestination = CursorUtil.getColumnIndexOrThrow(_cursor, "destination");
      final int _cursorIndexOfMemo = CursorUtil.getColumnIndexOrThrow(_cursor, "memo");
      final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
      final int _cursorIndexOfIsNotificationSent = CursorUtil.getColumnIndexOrThrow(_cursor, "isNotificationSent");
      final int _cursorIndexOfIsNotificationRead = CursorUtil.getColumnIndexOrThrow(_cursor, "isNotificationRead");
      final int _cursorIndexOfSharedScheduleId = CursorUtil.getColumnIndexOrThrow(_cursor, "sharedScheduleId");
      final int _cursorIndexOfIsSyncEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "isSyncEnabled");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
      final int _cursorIndexOfRespondedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "respondedAt");
      final SharedSchedule _result;
      if (_cursor.moveToFirst()) {
        _result = new SharedSchedule();
        _result.id = _cursor.getInt(_cursorIndexOfId);
        _result.originalScheduleId = _cursor.getInt(_cursorIndexOfOriginalScheduleId);
        if (_cursor.isNull(_cursorIndexOfCreatorUserId)) {
          _result.creatorUserId = null;
        } else {
          _result.creatorUserId = _cursor.getString(_cursorIndexOfCreatorUserId);
        }
        if (_cursor.isNull(_cursorIndexOfCreatorNickname)) {
          _result.creatorNickname = null;
        } else {
          _result.creatorNickname = _cursor.getString(_cursorIndexOfCreatorNickname);
        }
        if (_cursor.isNull(_cursorIndexOfInvitedUserId)) {
          _result.invitedUserId = null;
        } else {
          _result.invitedUserId = _cursor.getString(_cursorIndexOfInvitedUserId);
        }
        if (_cursor.isNull(_cursorIndexOfInvitedNickname)) {
          _result.invitedNickname = null;
        } else {
          _result.invitedNickname = _cursor.getString(_cursorIndexOfInvitedNickname);
        }
        if (_cursor.isNull(_cursorIndexOfTitle)) {
          _result.title = null;
        } else {
          _result.title = _cursor.getString(_cursorIndexOfTitle);
        }
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _result.date = null;
        } else {
          _result.date = _cursor.getString(_cursorIndexOfDate);
        }
        if (_cursor.isNull(_cursorIndexOfTime)) {
          _result.time = null;
        } else {
          _result.time = _cursor.getString(_cursorIndexOfTime);
        }
        if (_cursor.isNull(_cursorIndexOfDeparture)) {
          _result.departure = null;
        } else {
          _result.departure = _cursor.getString(_cursorIndexOfDeparture);
        }
        if (_cursor.isNull(_cursorIndexOfDestination)) {
          _result.destination = null;
        } else {
          _result.destination = _cursor.getString(_cursorIndexOfDestination);
        }
        if (_cursor.isNull(_cursorIndexOfMemo)) {
          _result.memo = null;
        } else {
          _result.memo = _cursor.getString(_cursorIndexOfMemo);
        }
        if (_cursor.isNull(_cursorIndexOfStatus)) {
          _result.status = null;
        } else {
          _result.status = _cursor.getString(_cursorIndexOfStatus);
        }
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsNotificationSent);
        _result.isNotificationSent = _tmp != 0;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsNotificationRead);
        _result.isNotificationRead = _tmp_1 != 0;
        _result.sharedScheduleId = _cursor.getInt(_cursorIndexOfSharedScheduleId);
        final int _tmp_2;
        _tmp_2 = _cursor.getInt(_cursorIndexOfIsSyncEnabled);
        _result.isSyncEnabled = _tmp_2 != 0;
        _result.createdAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _result.updatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _result.respondedAt = _cursor.getLong(_cursorIndexOfRespondedAt);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<SharedSchedule> getSharedSchedulesByScheduleId(final long scheduleId) {
    final String _sql = "SELECT * FROM shared_schedules WHERE originalScheduleId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, scheduleId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfOriginalScheduleId = CursorUtil.getColumnIndexOrThrow(_cursor, "originalScheduleId");
      final int _cursorIndexOfCreatorUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "creatorUserId");
      final int _cursorIndexOfCreatorNickname = CursorUtil.getColumnIndexOrThrow(_cursor, "creatorNickname");
      final int _cursorIndexOfInvitedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "invitedUserId");
      final int _cursorIndexOfInvitedNickname = CursorUtil.getColumnIndexOrThrow(_cursor, "invitedNickname");
      final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfTime = CursorUtil.getColumnIndexOrThrow(_cursor, "time");
      final int _cursorIndexOfDeparture = CursorUtil.getColumnIndexOrThrow(_cursor, "departure");
      final int _cursorIndexOfDestination = CursorUtil.getColumnIndexOrThrow(_cursor, "destination");
      final int _cursorIndexOfMemo = CursorUtil.getColumnIndexOrThrow(_cursor, "memo");
      final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
      final int _cursorIndexOfIsNotificationSent = CursorUtil.getColumnIndexOrThrow(_cursor, "isNotificationSent");
      final int _cursorIndexOfIsNotificationRead = CursorUtil.getColumnIndexOrThrow(_cursor, "isNotificationRead");
      final int _cursorIndexOfSharedScheduleId = CursorUtil.getColumnIndexOrThrow(_cursor, "sharedScheduleId");
      final int _cursorIndexOfIsSyncEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "isSyncEnabled");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
      final int _cursorIndexOfRespondedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "respondedAt");
      final List<SharedSchedule> _result = new ArrayList<SharedSchedule>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final SharedSchedule _item;
        _item = new SharedSchedule();
        _item.id = _cursor.getInt(_cursorIndexOfId);
        _item.originalScheduleId = _cursor.getInt(_cursorIndexOfOriginalScheduleId);
        if (_cursor.isNull(_cursorIndexOfCreatorUserId)) {
          _item.creatorUserId = null;
        } else {
          _item.creatorUserId = _cursor.getString(_cursorIndexOfCreatorUserId);
        }
        if (_cursor.isNull(_cursorIndexOfCreatorNickname)) {
          _item.creatorNickname = null;
        } else {
          _item.creatorNickname = _cursor.getString(_cursorIndexOfCreatorNickname);
        }
        if (_cursor.isNull(_cursorIndexOfInvitedUserId)) {
          _item.invitedUserId = null;
        } else {
          _item.invitedUserId = _cursor.getString(_cursorIndexOfInvitedUserId);
        }
        if (_cursor.isNull(_cursorIndexOfInvitedNickname)) {
          _item.invitedNickname = null;
        } else {
          _item.invitedNickname = _cursor.getString(_cursorIndexOfInvitedNickname);
        }
        if (_cursor.isNull(_cursorIndexOfTitle)) {
          _item.title = null;
        } else {
          _item.title = _cursor.getString(_cursorIndexOfTitle);
        }
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _item.date = null;
        } else {
          _item.date = _cursor.getString(_cursorIndexOfDate);
        }
        if (_cursor.isNull(_cursorIndexOfTime)) {
          _item.time = null;
        } else {
          _item.time = _cursor.getString(_cursorIndexOfTime);
        }
        if (_cursor.isNull(_cursorIndexOfDeparture)) {
          _item.departure = null;
        } else {
          _item.departure = _cursor.getString(_cursorIndexOfDeparture);
        }
        if (_cursor.isNull(_cursorIndexOfDestination)) {
          _item.destination = null;
        } else {
          _item.destination = _cursor.getString(_cursorIndexOfDestination);
        }
        if (_cursor.isNull(_cursorIndexOfMemo)) {
          _item.memo = null;
        } else {
          _item.memo = _cursor.getString(_cursorIndexOfMemo);
        }
        if (_cursor.isNull(_cursorIndexOfStatus)) {
          _item.status = null;
        } else {
          _item.status = _cursor.getString(_cursorIndexOfStatus);
        }
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsNotificationSent);
        _item.isNotificationSent = _tmp != 0;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsNotificationRead);
        _item.isNotificationRead = _tmp_1 != 0;
        _item.sharedScheduleId = _cursor.getInt(_cursorIndexOfSharedScheduleId);
        final int _tmp_2;
        _tmp_2 = _cursor.getInt(_cursorIndexOfIsSyncEnabled);
        _item.isSyncEnabled = _tmp_2 != 0;
        _item.createdAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.updatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.respondedAt = _cursor.getLong(_cursorIndexOfRespondedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<SharedSchedule> getSharedSchedulesByUserId(final String userId) {
    final String _sql = "SELECT * FROM shared_schedules WHERE invitedUserId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (userId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, userId);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfOriginalScheduleId = CursorUtil.getColumnIndexOrThrow(_cursor, "originalScheduleId");
      final int _cursorIndexOfCreatorUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "creatorUserId");
      final int _cursorIndexOfCreatorNickname = CursorUtil.getColumnIndexOrThrow(_cursor, "creatorNickname");
      final int _cursorIndexOfInvitedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "invitedUserId");
      final int _cursorIndexOfInvitedNickname = CursorUtil.getColumnIndexOrThrow(_cursor, "invitedNickname");
      final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfTime = CursorUtil.getColumnIndexOrThrow(_cursor, "time");
      final int _cursorIndexOfDeparture = CursorUtil.getColumnIndexOrThrow(_cursor, "departure");
      final int _cursorIndexOfDestination = CursorUtil.getColumnIndexOrThrow(_cursor, "destination");
      final int _cursorIndexOfMemo = CursorUtil.getColumnIndexOrThrow(_cursor, "memo");
      final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
      final int _cursorIndexOfIsNotificationSent = CursorUtil.getColumnIndexOrThrow(_cursor, "isNotificationSent");
      final int _cursorIndexOfIsNotificationRead = CursorUtil.getColumnIndexOrThrow(_cursor, "isNotificationRead");
      final int _cursorIndexOfSharedScheduleId = CursorUtil.getColumnIndexOrThrow(_cursor, "sharedScheduleId");
      final int _cursorIndexOfIsSyncEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "isSyncEnabled");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
      final int _cursorIndexOfRespondedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "respondedAt");
      final List<SharedSchedule> _result = new ArrayList<SharedSchedule>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final SharedSchedule _item;
        _item = new SharedSchedule();
        _item.id = _cursor.getInt(_cursorIndexOfId);
        _item.originalScheduleId = _cursor.getInt(_cursorIndexOfOriginalScheduleId);
        if (_cursor.isNull(_cursorIndexOfCreatorUserId)) {
          _item.creatorUserId = null;
        } else {
          _item.creatorUserId = _cursor.getString(_cursorIndexOfCreatorUserId);
        }
        if (_cursor.isNull(_cursorIndexOfCreatorNickname)) {
          _item.creatorNickname = null;
        } else {
          _item.creatorNickname = _cursor.getString(_cursorIndexOfCreatorNickname);
        }
        if (_cursor.isNull(_cursorIndexOfInvitedUserId)) {
          _item.invitedUserId = null;
        } else {
          _item.invitedUserId = _cursor.getString(_cursorIndexOfInvitedUserId);
        }
        if (_cursor.isNull(_cursorIndexOfInvitedNickname)) {
          _item.invitedNickname = null;
        } else {
          _item.invitedNickname = _cursor.getString(_cursorIndexOfInvitedNickname);
        }
        if (_cursor.isNull(_cursorIndexOfTitle)) {
          _item.title = null;
        } else {
          _item.title = _cursor.getString(_cursorIndexOfTitle);
        }
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _item.date = null;
        } else {
          _item.date = _cursor.getString(_cursorIndexOfDate);
        }
        if (_cursor.isNull(_cursorIndexOfTime)) {
          _item.time = null;
        } else {
          _item.time = _cursor.getString(_cursorIndexOfTime);
        }
        if (_cursor.isNull(_cursorIndexOfDeparture)) {
          _item.departure = null;
        } else {
          _item.departure = _cursor.getString(_cursorIndexOfDeparture);
        }
        if (_cursor.isNull(_cursorIndexOfDestination)) {
          _item.destination = null;
        } else {
          _item.destination = _cursor.getString(_cursorIndexOfDestination);
        }
        if (_cursor.isNull(_cursorIndexOfMemo)) {
          _item.memo = null;
        } else {
          _item.memo = _cursor.getString(_cursorIndexOfMemo);
        }
        if (_cursor.isNull(_cursorIndexOfStatus)) {
          _item.status = null;
        } else {
          _item.status = _cursor.getString(_cursorIndexOfStatus);
        }
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsNotificationSent);
        _item.isNotificationSent = _tmp != 0;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsNotificationRead);
        _item.isNotificationRead = _tmp_1 != 0;
        _item.sharedScheduleId = _cursor.getInt(_cursorIndexOfSharedScheduleId);
        final int _tmp_2;
        _tmp_2 = _cursor.getInt(_cursorIndexOfIsSyncEnabled);
        _item.isSyncEnabled = _tmp_2 != 0;
        _item.createdAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.updatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.respondedAt = _cursor.getLong(_cursorIndexOfRespondedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public SharedSchedule getSharedSchedule(final long scheduleId, final String userId) {
    final String _sql = "SELECT * FROM shared_schedules WHERE originalScheduleId = ? AND invitedUserId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, scheduleId);
    _argIndex = 2;
    if (userId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, userId);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfOriginalScheduleId = CursorUtil.getColumnIndexOrThrow(_cursor, "originalScheduleId");
      final int _cursorIndexOfCreatorUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "creatorUserId");
      final int _cursorIndexOfCreatorNickname = CursorUtil.getColumnIndexOrThrow(_cursor, "creatorNickname");
      final int _cursorIndexOfInvitedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "invitedUserId");
      final int _cursorIndexOfInvitedNickname = CursorUtil.getColumnIndexOrThrow(_cursor, "invitedNickname");
      final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfTime = CursorUtil.getColumnIndexOrThrow(_cursor, "time");
      final int _cursorIndexOfDeparture = CursorUtil.getColumnIndexOrThrow(_cursor, "departure");
      final int _cursorIndexOfDestination = CursorUtil.getColumnIndexOrThrow(_cursor, "destination");
      final int _cursorIndexOfMemo = CursorUtil.getColumnIndexOrThrow(_cursor, "memo");
      final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
      final int _cursorIndexOfIsNotificationSent = CursorUtil.getColumnIndexOrThrow(_cursor, "isNotificationSent");
      final int _cursorIndexOfIsNotificationRead = CursorUtil.getColumnIndexOrThrow(_cursor, "isNotificationRead");
      final int _cursorIndexOfSharedScheduleId = CursorUtil.getColumnIndexOrThrow(_cursor, "sharedScheduleId");
      final int _cursorIndexOfIsSyncEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "isSyncEnabled");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
      final int _cursorIndexOfRespondedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "respondedAt");
      final SharedSchedule _result;
      if (_cursor.moveToFirst()) {
        _result = new SharedSchedule();
        _result.id = _cursor.getInt(_cursorIndexOfId);
        _result.originalScheduleId = _cursor.getInt(_cursorIndexOfOriginalScheduleId);
        if (_cursor.isNull(_cursorIndexOfCreatorUserId)) {
          _result.creatorUserId = null;
        } else {
          _result.creatorUserId = _cursor.getString(_cursorIndexOfCreatorUserId);
        }
        if (_cursor.isNull(_cursorIndexOfCreatorNickname)) {
          _result.creatorNickname = null;
        } else {
          _result.creatorNickname = _cursor.getString(_cursorIndexOfCreatorNickname);
        }
        if (_cursor.isNull(_cursorIndexOfInvitedUserId)) {
          _result.invitedUserId = null;
        } else {
          _result.invitedUserId = _cursor.getString(_cursorIndexOfInvitedUserId);
        }
        if (_cursor.isNull(_cursorIndexOfInvitedNickname)) {
          _result.invitedNickname = null;
        } else {
          _result.invitedNickname = _cursor.getString(_cursorIndexOfInvitedNickname);
        }
        if (_cursor.isNull(_cursorIndexOfTitle)) {
          _result.title = null;
        } else {
          _result.title = _cursor.getString(_cursorIndexOfTitle);
        }
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _result.date = null;
        } else {
          _result.date = _cursor.getString(_cursorIndexOfDate);
        }
        if (_cursor.isNull(_cursorIndexOfTime)) {
          _result.time = null;
        } else {
          _result.time = _cursor.getString(_cursorIndexOfTime);
        }
        if (_cursor.isNull(_cursorIndexOfDeparture)) {
          _result.departure = null;
        } else {
          _result.departure = _cursor.getString(_cursorIndexOfDeparture);
        }
        if (_cursor.isNull(_cursorIndexOfDestination)) {
          _result.destination = null;
        } else {
          _result.destination = _cursor.getString(_cursorIndexOfDestination);
        }
        if (_cursor.isNull(_cursorIndexOfMemo)) {
          _result.memo = null;
        } else {
          _result.memo = _cursor.getString(_cursorIndexOfMemo);
        }
        if (_cursor.isNull(_cursorIndexOfStatus)) {
          _result.status = null;
        } else {
          _result.status = _cursor.getString(_cursorIndexOfStatus);
        }
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsNotificationSent);
        _result.isNotificationSent = _tmp != 0;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsNotificationRead);
        _result.isNotificationRead = _tmp_1 != 0;
        _result.sharedScheduleId = _cursor.getInt(_cursorIndexOfSharedScheduleId);
        final int _tmp_2;
        _tmp_2 = _cursor.getInt(_cursorIndexOfIsSyncEnabled);
        _result.isSyncEnabled = _tmp_2 != 0;
        _result.createdAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _result.updatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _result.respondedAt = _cursor.getLong(_cursorIndexOfRespondedAt);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
