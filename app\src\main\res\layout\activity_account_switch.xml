<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_secondary">

    <!-- 헤더 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        style="@style/TimeMateAppBar"
        android:orientation="vertical"
        android:padding="20dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="계정 전환"
            style="@style/TimeMateTextTitle"
            android:gravity="center"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="사용할 계정을 선택하거나 새 계정을 만드세요\n계정별로 완전히 분리된 데이터를 가집니다"
            style="@style/TimeMateTextBody"
            android:textColor="@color/text_secondary"
            android:gravity="center"
            android:lineSpacingExtra="2dp" />

    </LinearLayout>

    <!-- 현재 계정 정보 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/pastel_mint"
            android:orientation="vertical"
            android:padding="20dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="🔐 현재 계정"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/textCurrentUser"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="로그인되지 않음"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:layout_marginBottom="12dp" />

            <Button
                android:id="@+id/btnCurrentAccount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="현재 계정으로 계속"
                android:textColor="@color/white" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- 계정 목록 섹션 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:layout_marginHorizontal="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="👥 다른 계정"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:layout_marginBottom="12dp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerAccounts"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:clipToPadding="false"
            android:paddingBottom="16dp" />

    </LinearLayout>

    <!-- 하단 액션 버튼 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@color/card_background"
        android:elevation="8dp">

        <Button
            android:id="@+id/btnAddAccount"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="새 계정 만들기"
            android:textColor="@color/white" />

    </LinearLayout>

</LinearLayout>
