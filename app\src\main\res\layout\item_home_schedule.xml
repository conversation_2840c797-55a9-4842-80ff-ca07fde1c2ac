<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:background="@drawable/ios_card_background"
    android:padding="16dp"
    android:layout_marginBottom="8dp"
    android:gravity="center_vertical"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <!-- 일정 색상 인디케이터 -->
    <View
        android:id="@+id/scheduleIndicator"
        android:layout_width="4dp"
        android:layout_height="40dp"
        android:background="@color/ios_blue"
        android:layout_marginEnd="12dp" />

    <!-- 일정 정보 -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/textScheduleTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="회의 참석"
            style="@style/TimeMateTextBody"
            android:textColor="@color/text_primary"
            android:maxLines="1"
            android:ellipsize="end" />

        <TextView
            android:id="@+id/textScheduleTime"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="⏰ 09:00"
            style="@style/TimeMateTextCaption"
            android:textColor="@color/text_secondary"
            android:layout_marginTop="4dp" />

        <TextView
            android:id="@+id/textScheduleLocation"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="📍 회사 회의실"
            style="@style/TimeMateTextCaption"
            android:textColor="@color/text_hint"
            android:layout_marginTop="2dp"
            android:maxLines="1"
            android:ellipsize="end" />

    </LinearLayout>

    <!-- 화살표 아이콘 -->
    <ImageView
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:src="@drawable/ic_arrow_right"
        android:tint="@color/text_hint" />

</LinearLayout>
