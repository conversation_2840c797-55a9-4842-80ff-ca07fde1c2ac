<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="2dp"
    app:cardBackgroundColor="@color/background_card"
    app:strokeWidth="0.5dp"
    app:strokeColor="@color/ios_gray5">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- 장소 이미지 영역 -->
        <FrameLayout
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginEnd="16dp">

            <!-- 실제 이미지 -->
            <ImageView
                android:id="@+id/imagePlace"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:background="@drawable/ios_card_background"
                android:contentDescription="장소 이미지" />

            <!-- 이미지 로딩 중 표시 -->
            <LinearLayout
                android:id="@+id/layoutImageLoading"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@color/ios_gray6"
                android:visibility="gone">

                <ProgressBar
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginBottom="4dp"
                    style="?android:attr/progressBarStyleSmall" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="로딩중"
                    style="@style/iOSCaption"
                    android:textColor="@color/text_tertiary" />

            </LinearLayout>

        </FrameLayout>

        <!-- 장소 정보 영역 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- 장소명과 카테고리 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="4dp">

                <TextView
                    android:id="@+id/textPlaceName"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="장소명"
                    style="@style/iOSSubheadline"
                    android:textColor="@color/text_primary"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/textDistance"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="500m"
                    style="@style/iOSCaption"
                    android:textColor="@color/ios_blue"
                    android:background="@drawable/ios_chip_background"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="2dp"
                    android:layout_marginStart="8dp"
                    android:visibility="gone" />

            </LinearLayout>

            <!-- 카테고리 -->
            <TextView
                android:id="@+id/textCategory"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="카테고리"
                style="@style/iOSFootnote"
                android:textColor="@color/text_secondary"
                android:layout_marginBottom="4dp" />

            <!-- 주소 -->
            <TextView
                android:id="@+id/textAddress"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="주소"
                style="@style/iOSCallout"
                android:textColor="@color/text_secondary"
                android:maxLines="2"
                android:ellipsize="end"
                android:layout_marginBottom="4dp" />

            <!-- 전화번호 -->
            <TextView
                android:id="@+id/textPhone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="전화번호"
                style="@style/iOSFootnote"
                android:textColor="@color/ios_blue"
                android:drawableStart="@drawable/ic_phone"
                android:drawablePadding="4dp"
                android:visibility="gone" />

        </LinearLayout>

        <!-- 더보기 아이콘 -->
        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_arrow_forward"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="8dp"
            app:tint="@color/text_tertiary"
            android:contentDescription="상세보기" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
