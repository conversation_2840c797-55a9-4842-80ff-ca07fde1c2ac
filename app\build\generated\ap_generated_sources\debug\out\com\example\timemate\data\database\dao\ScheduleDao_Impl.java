package com.example.timemate.data.database.dao;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.timemate.data.model.Schedule;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class ScheduleDao_Impl implements ScheduleDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<Schedule> __insertionAdapterOfSchedule;

  private final EntityDeletionOrUpdateAdapter<Schedule> __deletionAdapterOfSchedule;

  private final EntityDeletionOrUpdateAdapter<Schedule> __updateAdapterOfSchedule;

  private final SharedSQLiteStatement __preparedStmtOfUpdateScheduleCompletion;

  private final SharedSQLiteStatement __preparedStmtOfDeleteById;

  private final SharedSQLiteStatement __preparedStmtOfDeleteByUserId;

  public ScheduleDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfSchedule = new EntityInsertionAdapter<Schedule>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `schedules` (`id`,`userId`,`title`,`date`,`time`,`departure`,`destination`,`memo`,`routeInfo`,`selectedTransportModes`,`isCompleted`,`createdAt`,`updatedAt`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement, final Schedule entity) {
        statement.bindLong(1, entity.id);
        if (entity.userId == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.userId);
        }
        if (entity.title == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.title);
        }
        if (entity.date == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.date);
        }
        if (entity.time == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.time);
        }
        if (entity.departure == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.departure);
        }
        if (entity.destination == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.destination);
        }
        if (entity.memo == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.memo);
        }
        if (entity.routeInfo == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.routeInfo);
        }
        if (entity.selectedTransportModes == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.selectedTransportModes);
        }
        final int _tmp = entity.isCompleted ? 1 : 0;
        statement.bindLong(11, _tmp);
        statement.bindLong(12, entity.createdAt);
        statement.bindLong(13, entity.updatedAt);
      }
    };
    this.__deletionAdapterOfSchedule = new EntityDeletionOrUpdateAdapter<Schedule>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `schedules` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement, final Schedule entity) {
        statement.bindLong(1, entity.id);
      }
    };
    this.__updateAdapterOfSchedule = new EntityDeletionOrUpdateAdapter<Schedule>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `schedules` SET `id` = ?,`userId` = ?,`title` = ?,`date` = ?,`time` = ?,`departure` = ?,`destination` = ?,`memo` = ?,`routeInfo` = ?,`selectedTransportModes` = ?,`isCompleted` = ?,`createdAt` = ?,`updatedAt` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement, final Schedule entity) {
        statement.bindLong(1, entity.id);
        if (entity.userId == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.userId);
        }
        if (entity.title == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.title);
        }
        if (entity.date == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.date);
        }
        if (entity.time == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.time);
        }
        if (entity.departure == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.departure);
        }
        if (entity.destination == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.destination);
        }
        if (entity.memo == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.memo);
        }
        if (entity.routeInfo == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.routeInfo);
        }
        if (entity.selectedTransportModes == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.selectedTransportModes);
        }
        final int _tmp = entity.isCompleted ? 1 : 0;
        statement.bindLong(11, _tmp);
        statement.bindLong(12, entity.createdAt);
        statement.bindLong(13, entity.updatedAt);
        statement.bindLong(14, entity.id);
      }
    };
    this.__preparedStmtOfUpdateScheduleCompletion = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE schedules SET isCompleted = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM schedules WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteByUserId = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM schedules WHERE userId = ?";
        return _query;
      }
    };
  }

  @Override
  public long insert(final Schedule schedule) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      final long _result = __insertionAdapterOfSchedule.insertAndReturnId(schedule);
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public int delete(final Schedule schedule) {
    __db.assertNotSuspendingTransaction();
    int _total = 0;
    __db.beginTransaction();
    try {
      _total += __deletionAdapterOfSchedule.handle(schedule);
      __db.setTransactionSuccessful();
      return _total;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public int update(final Schedule schedule) {
    __db.assertNotSuspendingTransaction();
    int _total = 0;
    __db.beginTransaction();
    try {
      _total += __updateAdapterOfSchedule.handle(schedule);
      __db.setTransactionSuccessful();
      return _total;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public int updateScheduleCompletion(final long id, final boolean completed) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateScheduleCompletion.acquire();
    int _argIndex = 1;
    final int _tmp = completed ? 1 : 0;
    _stmt.bindLong(_argIndex, _tmp);
    _argIndex = 2;
    _stmt.bindLong(_argIndex, id);
    try {
      __db.beginTransaction();
      try {
        final int _result = _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
        return _result;
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfUpdateScheduleCompletion.release(_stmt);
    }
  }

  @Override
  public int deleteById(final long id) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteById.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, id);
    try {
      __db.beginTransaction();
      try {
        final int _result = _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
        return _result;
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfDeleteById.release(_stmt);
    }
  }

  @Override
  public int deleteByUserId(final String userId) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteByUserId.acquire();
    int _argIndex = 1;
    if (userId == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, userId);
    }
    try {
      __db.beginTransaction();
      try {
        final int _result = _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
        return _result;
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfDeleteByUserId.release(_stmt);
    }
  }

  @Override
  public Schedule getScheduleById(final long id) {
    final String _sql = "SELECT * FROM schedules WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
      final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfTime = CursorUtil.getColumnIndexOrThrow(_cursor, "time");
      final int _cursorIndexOfDeparture = CursorUtil.getColumnIndexOrThrow(_cursor, "departure");
      final int _cursorIndexOfDestination = CursorUtil.getColumnIndexOrThrow(_cursor, "destination");
      final int _cursorIndexOfMemo = CursorUtil.getColumnIndexOrThrow(_cursor, "memo");
      final int _cursorIndexOfRouteInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "routeInfo");
      final int _cursorIndexOfSelectedTransportModes = CursorUtil.getColumnIndexOrThrow(_cursor, "selectedTransportModes");
      final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
      final Schedule _result;
      if (_cursor.moveToFirst()) {
        _result = new Schedule();
        _result.id = _cursor.getInt(_cursorIndexOfId);
        if (_cursor.isNull(_cursorIndexOfUserId)) {
          _result.userId = null;
        } else {
          _result.userId = _cursor.getString(_cursorIndexOfUserId);
        }
        if (_cursor.isNull(_cursorIndexOfTitle)) {
          _result.title = null;
        } else {
          _result.title = _cursor.getString(_cursorIndexOfTitle);
        }
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _result.date = null;
        } else {
          _result.date = _cursor.getString(_cursorIndexOfDate);
        }
        if (_cursor.isNull(_cursorIndexOfTime)) {
          _result.time = null;
        } else {
          _result.time = _cursor.getString(_cursorIndexOfTime);
        }
        if (_cursor.isNull(_cursorIndexOfDeparture)) {
          _result.departure = null;
        } else {
          _result.departure = _cursor.getString(_cursorIndexOfDeparture);
        }
        if (_cursor.isNull(_cursorIndexOfDestination)) {
          _result.destination = null;
        } else {
          _result.destination = _cursor.getString(_cursorIndexOfDestination);
        }
        if (_cursor.isNull(_cursorIndexOfMemo)) {
          _result.memo = null;
        } else {
          _result.memo = _cursor.getString(_cursorIndexOfMemo);
        }
        if (_cursor.isNull(_cursorIndexOfRouteInfo)) {
          _result.routeInfo = null;
        } else {
          _result.routeInfo = _cursor.getString(_cursorIndexOfRouteInfo);
        }
        if (_cursor.isNull(_cursorIndexOfSelectedTransportModes)) {
          _result.selectedTransportModes = null;
        } else {
          _result.selectedTransportModes = _cursor.getString(_cursorIndexOfSelectedTransportModes);
        }
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
        _result.isCompleted = _tmp != 0;
        _result.createdAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _result.updatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<Schedule> getSchedulesByUserId(final String userId) {
    final String _sql = "SELECT * FROM schedules WHERE userId = ? ORDER BY date ASC, time ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (userId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, userId);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
      final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfTime = CursorUtil.getColumnIndexOrThrow(_cursor, "time");
      final int _cursorIndexOfDeparture = CursorUtil.getColumnIndexOrThrow(_cursor, "departure");
      final int _cursorIndexOfDestination = CursorUtil.getColumnIndexOrThrow(_cursor, "destination");
      final int _cursorIndexOfMemo = CursorUtil.getColumnIndexOrThrow(_cursor, "memo");
      final int _cursorIndexOfRouteInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "routeInfo");
      final int _cursorIndexOfSelectedTransportModes = CursorUtil.getColumnIndexOrThrow(_cursor, "selectedTransportModes");
      final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
      final List<Schedule> _result = new ArrayList<Schedule>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final Schedule _item;
        _item = new Schedule();
        _item.id = _cursor.getInt(_cursorIndexOfId);
        if (_cursor.isNull(_cursorIndexOfUserId)) {
          _item.userId = null;
        } else {
          _item.userId = _cursor.getString(_cursorIndexOfUserId);
        }
        if (_cursor.isNull(_cursorIndexOfTitle)) {
          _item.title = null;
        } else {
          _item.title = _cursor.getString(_cursorIndexOfTitle);
        }
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _item.date = null;
        } else {
          _item.date = _cursor.getString(_cursorIndexOfDate);
        }
        if (_cursor.isNull(_cursorIndexOfTime)) {
          _item.time = null;
        } else {
          _item.time = _cursor.getString(_cursorIndexOfTime);
        }
        if (_cursor.isNull(_cursorIndexOfDeparture)) {
          _item.departure = null;
        } else {
          _item.departure = _cursor.getString(_cursorIndexOfDeparture);
        }
        if (_cursor.isNull(_cursorIndexOfDestination)) {
          _item.destination = null;
        } else {
          _item.destination = _cursor.getString(_cursorIndexOfDestination);
        }
        if (_cursor.isNull(_cursorIndexOfMemo)) {
          _item.memo = null;
        } else {
          _item.memo = _cursor.getString(_cursorIndexOfMemo);
        }
        if (_cursor.isNull(_cursorIndexOfRouteInfo)) {
          _item.routeInfo = null;
        } else {
          _item.routeInfo = _cursor.getString(_cursorIndexOfRouteInfo);
        }
        if (_cursor.isNull(_cursorIndexOfSelectedTransportModes)) {
          _item.selectedTransportModes = null;
        } else {
          _item.selectedTransportModes = _cursor.getString(_cursorIndexOfSelectedTransportModes);
        }
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
        _item.isCompleted = _tmp != 0;
        _item.createdAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.updatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<Schedule> getSchedulesByUserIdFromToday(final String userId, final String todayDate) {
    final String _sql = "SELECT * FROM schedules WHERE userId = ? AND date >= ? ORDER BY date ASC, time ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (userId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, userId);
    }
    _argIndex = 2;
    if (todayDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, todayDate);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
      final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfTime = CursorUtil.getColumnIndexOrThrow(_cursor, "time");
      final int _cursorIndexOfDeparture = CursorUtil.getColumnIndexOrThrow(_cursor, "departure");
      final int _cursorIndexOfDestination = CursorUtil.getColumnIndexOrThrow(_cursor, "destination");
      final int _cursorIndexOfMemo = CursorUtil.getColumnIndexOrThrow(_cursor, "memo");
      final int _cursorIndexOfRouteInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "routeInfo");
      final int _cursorIndexOfSelectedTransportModes = CursorUtil.getColumnIndexOrThrow(_cursor, "selectedTransportModes");
      final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
      final List<Schedule> _result = new ArrayList<Schedule>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final Schedule _item;
        _item = new Schedule();
        _item.id = _cursor.getInt(_cursorIndexOfId);
        if (_cursor.isNull(_cursorIndexOfUserId)) {
          _item.userId = null;
        } else {
          _item.userId = _cursor.getString(_cursorIndexOfUserId);
        }
        if (_cursor.isNull(_cursorIndexOfTitle)) {
          _item.title = null;
        } else {
          _item.title = _cursor.getString(_cursorIndexOfTitle);
        }
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _item.date = null;
        } else {
          _item.date = _cursor.getString(_cursorIndexOfDate);
        }
        if (_cursor.isNull(_cursorIndexOfTime)) {
          _item.time = null;
        } else {
          _item.time = _cursor.getString(_cursorIndexOfTime);
        }
        if (_cursor.isNull(_cursorIndexOfDeparture)) {
          _item.departure = null;
        } else {
          _item.departure = _cursor.getString(_cursorIndexOfDeparture);
        }
        if (_cursor.isNull(_cursorIndexOfDestination)) {
          _item.destination = null;
        } else {
          _item.destination = _cursor.getString(_cursorIndexOfDestination);
        }
        if (_cursor.isNull(_cursorIndexOfMemo)) {
          _item.memo = null;
        } else {
          _item.memo = _cursor.getString(_cursorIndexOfMemo);
        }
        if (_cursor.isNull(_cursorIndexOfRouteInfo)) {
          _item.routeInfo = null;
        } else {
          _item.routeInfo = _cursor.getString(_cursorIndexOfRouteInfo);
        }
        if (_cursor.isNull(_cursorIndexOfSelectedTransportModes)) {
          _item.selectedTransportModes = null;
        } else {
          _item.selectedTransportModes = _cursor.getString(_cursorIndexOfSelectedTransportModes);
        }
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
        _item.isCompleted = _tmp != 0;
        _item.createdAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.updatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<Schedule> getSchedulesByUserAndDateRange(final String userId, final String startDate,
      final String endDate) {
    final String _sql = "SELECT * FROM schedules WHERE userId = ? AND date BETWEEN ? AND ? ORDER BY date ASC, time ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    if (userId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, userId);
    }
    _argIndex = 2;
    if (startDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, startDate);
    }
    _argIndex = 3;
    if (endDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, endDate);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
      final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfTime = CursorUtil.getColumnIndexOrThrow(_cursor, "time");
      final int _cursorIndexOfDeparture = CursorUtil.getColumnIndexOrThrow(_cursor, "departure");
      final int _cursorIndexOfDestination = CursorUtil.getColumnIndexOrThrow(_cursor, "destination");
      final int _cursorIndexOfMemo = CursorUtil.getColumnIndexOrThrow(_cursor, "memo");
      final int _cursorIndexOfRouteInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "routeInfo");
      final int _cursorIndexOfSelectedTransportModes = CursorUtil.getColumnIndexOrThrow(_cursor, "selectedTransportModes");
      final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
      final List<Schedule> _result = new ArrayList<Schedule>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final Schedule _item;
        _item = new Schedule();
        _item.id = _cursor.getInt(_cursorIndexOfId);
        if (_cursor.isNull(_cursorIndexOfUserId)) {
          _item.userId = null;
        } else {
          _item.userId = _cursor.getString(_cursorIndexOfUserId);
        }
        if (_cursor.isNull(_cursorIndexOfTitle)) {
          _item.title = null;
        } else {
          _item.title = _cursor.getString(_cursorIndexOfTitle);
        }
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _item.date = null;
        } else {
          _item.date = _cursor.getString(_cursorIndexOfDate);
        }
        if (_cursor.isNull(_cursorIndexOfTime)) {
          _item.time = null;
        } else {
          _item.time = _cursor.getString(_cursorIndexOfTime);
        }
        if (_cursor.isNull(_cursorIndexOfDeparture)) {
          _item.departure = null;
        } else {
          _item.departure = _cursor.getString(_cursorIndexOfDeparture);
        }
        if (_cursor.isNull(_cursorIndexOfDestination)) {
          _item.destination = null;
        } else {
          _item.destination = _cursor.getString(_cursorIndexOfDestination);
        }
        if (_cursor.isNull(_cursorIndexOfMemo)) {
          _item.memo = null;
        } else {
          _item.memo = _cursor.getString(_cursorIndexOfMemo);
        }
        if (_cursor.isNull(_cursorIndexOfRouteInfo)) {
          _item.routeInfo = null;
        } else {
          _item.routeInfo = _cursor.getString(_cursorIndexOfRouteInfo);
        }
        if (_cursor.isNull(_cursorIndexOfSelectedTransportModes)) {
          _item.selectedTransportModes = null;
        } else {
          _item.selectedTransportModes = _cursor.getString(_cursorIndexOfSelectedTransportModes);
        }
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
        _item.isCompleted = _tmp != 0;
        _item.createdAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.updatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<Schedule> getSchedulesByDateRange(final String startDate, final String endDate) {
    final String _sql = "SELECT * FROM schedules WHERE date BETWEEN ? AND ? ORDER BY date ASC, time ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (startDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, startDate);
    }
    _argIndex = 2;
    if (endDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, endDate);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
      final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfTime = CursorUtil.getColumnIndexOrThrow(_cursor, "time");
      final int _cursorIndexOfDeparture = CursorUtil.getColumnIndexOrThrow(_cursor, "departure");
      final int _cursorIndexOfDestination = CursorUtil.getColumnIndexOrThrow(_cursor, "destination");
      final int _cursorIndexOfMemo = CursorUtil.getColumnIndexOrThrow(_cursor, "memo");
      final int _cursorIndexOfRouteInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "routeInfo");
      final int _cursorIndexOfSelectedTransportModes = CursorUtil.getColumnIndexOrThrow(_cursor, "selectedTransportModes");
      final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
      final List<Schedule> _result = new ArrayList<Schedule>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final Schedule _item;
        _item = new Schedule();
        _item.id = _cursor.getInt(_cursorIndexOfId);
        if (_cursor.isNull(_cursorIndexOfUserId)) {
          _item.userId = null;
        } else {
          _item.userId = _cursor.getString(_cursorIndexOfUserId);
        }
        if (_cursor.isNull(_cursorIndexOfTitle)) {
          _item.title = null;
        } else {
          _item.title = _cursor.getString(_cursorIndexOfTitle);
        }
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _item.date = null;
        } else {
          _item.date = _cursor.getString(_cursorIndexOfDate);
        }
        if (_cursor.isNull(_cursorIndexOfTime)) {
          _item.time = null;
        } else {
          _item.time = _cursor.getString(_cursorIndexOfTime);
        }
        if (_cursor.isNull(_cursorIndexOfDeparture)) {
          _item.departure = null;
        } else {
          _item.departure = _cursor.getString(_cursorIndexOfDeparture);
        }
        if (_cursor.isNull(_cursorIndexOfDestination)) {
          _item.destination = null;
        } else {
          _item.destination = _cursor.getString(_cursorIndexOfDestination);
        }
        if (_cursor.isNull(_cursorIndexOfMemo)) {
          _item.memo = null;
        } else {
          _item.memo = _cursor.getString(_cursorIndexOfMemo);
        }
        if (_cursor.isNull(_cursorIndexOfRouteInfo)) {
          _item.routeInfo = null;
        } else {
          _item.routeInfo = _cursor.getString(_cursorIndexOfRouteInfo);
        }
        if (_cursor.isNull(_cursorIndexOfSelectedTransportModes)) {
          _item.selectedTransportModes = null;
        } else {
          _item.selectedTransportModes = _cursor.getString(_cursorIndexOfSelectedTransportModes);
        }
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
        _item.isCompleted = _tmp != 0;
        _item.createdAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.updatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<Schedule> getSchedulesByDate(final String targetDate) {
    final String _sql = "SELECT * FROM schedules WHERE date = ? ORDER BY date ASC, time ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (targetDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, targetDate);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
      final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfTime = CursorUtil.getColumnIndexOrThrow(_cursor, "time");
      final int _cursorIndexOfDeparture = CursorUtil.getColumnIndexOrThrow(_cursor, "departure");
      final int _cursorIndexOfDestination = CursorUtil.getColumnIndexOrThrow(_cursor, "destination");
      final int _cursorIndexOfMemo = CursorUtil.getColumnIndexOrThrow(_cursor, "memo");
      final int _cursorIndexOfRouteInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "routeInfo");
      final int _cursorIndexOfSelectedTransportModes = CursorUtil.getColumnIndexOrThrow(_cursor, "selectedTransportModes");
      final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
      final List<Schedule> _result = new ArrayList<Schedule>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final Schedule _item;
        _item = new Schedule();
        _item.id = _cursor.getInt(_cursorIndexOfId);
        if (_cursor.isNull(_cursorIndexOfUserId)) {
          _item.userId = null;
        } else {
          _item.userId = _cursor.getString(_cursorIndexOfUserId);
        }
        if (_cursor.isNull(_cursorIndexOfTitle)) {
          _item.title = null;
        } else {
          _item.title = _cursor.getString(_cursorIndexOfTitle);
        }
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _item.date = null;
        } else {
          _item.date = _cursor.getString(_cursorIndexOfDate);
        }
        if (_cursor.isNull(_cursorIndexOfTime)) {
          _item.time = null;
        } else {
          _item.time = _cursor.getString(_cursorIndexOfTime);
        }
        if (_cursor.isNull(_cursorIndexOfDeparture)) {
          _item.departure = null;
        } else {
          _item.departure = _cursor.getString(_cursorIndexOfDeparture);
        }
        if (_cursor.isNull(_cursorIndexOfDestination)) {
          _item.destination = null;
        } else {
          _item.destination = _cursor.getString(_cursorIndexOfDestination);
        }
        if (_cursor.isNull(_cursorIndexOfMemo)) {
          _item.memo = null;
        } else {
          _item.memo = _cursor.getString(_cursorIndexOfMemo);
        }
        if (_cursor.isNull(_cursorIndexOfRouteInfo)) {
          _item.routeInfo = null;
        } else {
          _item.routeInfo = _cursor.getString(_cursorIndexOfRouteInfo);
        }
        if (_cursor.isNull(_cursorIndexOfSelectedTransportModes)) {
          _item.selectedTransportModes = null;
        } else {
          _item.selectedTransportModes = _cursor.getString(_cursorIndexOfSelectedTransportModes);
        }
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
        _item.isCompleted = _tmp != 0;
        _item.createdAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.updatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<Schedule> getAllSchedules() {
    final String _sql = "SELECT * FROM schedules ORDER BY date ASC, time ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
      final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfTime = CursorUtil.getColumnIndexOrThrow(_cursor, "time");
      final int _cursorIndexOfDeparture = CursorUtil.getColumnIndexOrThrow(_cursor, "departure");
      final int _cursorIndexOfDestination = CursorUtil.getColumnIndexOrThrow(_cursor, "destination");
      final int _cursorIndexOfMemo = CursorUtil.getColumnIndexOrThrow(_cursor, "memo");
      final int _cursorIndexOfRouteInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "routeInfo");
      final int _cursorIndexOfSelectedTransportModes = CursorUtil.getColumnIndexOrThrow(_cursor, "selectedTransportModes");
      final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
      final List<Schedule> _result = new ArrayList<Schedule>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final Schedule _item;
        _item = new Schedule();
        _item.id = _cursor.getInt(_cursorIndexOfId);
        if (_cursor.isNull(_cursorIndexOfUserId)) {
          _item.userId = null;
        } else {
          _item.userId = _cursor.getString(_cursorIndexOfUserId);
        }
        if (_cursor.isNull(_cursorIndexOfTitle)) {
          _item.title = null;
        } else {
          _item.title = _cursor.getString(_cursorIndexOfTitle);
        }
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _item.date = null;
        } else {
          _item.date = _cursor.getString(_cursorIndexOfDate);
        }
        if (_cursor.isNull(_cursorIndexOfTime)) {
          _item.time = null;
        } else {
          _item.time = _cursor.getString(_cursorIndexOfTime);
        }
        if (_cursor.isNull(_cursorIndexOfDeparture)) {
          _item.departure = null;
        } else {
          _item.departure = _cursor.getString(_cursorIndexOfDeparture);
        }
        if (_cursor.isNull(_cursorIndexOfDestination)) {
          _item.destination = null;
        } else {
          _item.destination = _cursor.getString(_cursorIndexOfDestination);
        }
        if (_cursor.isNull(_cursorIndexOfMemo)) {
          _item.memo = null;
        } else {
          _item.memo = _cursor.getString(_cursorIndexOfMemo);
        }
        if (_cursor.isNull(_cursorIndexOfRouteInfo)) {
          _item.routeInfo = null;
        } else {
          _item.routeInfo = _cursor.getString(_cursorIndexOfRouteInfo);
        }
        if (_cursor.isNull(_cursorIndexOfSelectedTransportModes)) {
          _item.selectedTransportModes = null;
        } else {
          _item.selectedTransportModes = _cursor.getString(_cursorIndexOfSelectedTransportModes);
        }
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
        _item.isCompleted = _tmp != 0;
        _item.createdAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.updatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<Schedule>> getSchedulesByUserIdLive(final String userId) {
    final String _sql = "SELECT * FROM schedules WHERE userId = ? ORDER BY date ASC, time ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (userId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, userId);
    }
    return __db.getInvalidationTracker().createLiveData(new String[] {"schedules"}, false, new Callable<List<Schedule>>() {
      @Override
      @Nullable
      public List<Schedule> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfTime = CursorUtil.getColumnIndexOrThrow(_cursor, "time");
          final int _cursorIndexOfDeparture = CursorUtil.getColumnIndexOrThrow(_cursor, "departure");
          final int _cursorIndexOfDestination = CursorUtil.getColumnIndexOrThrow(_cursor, "destination");
          final int _cursorIndexOfMemo = CursorUtil.getColumnIndexOrThrow(_cursor, "memo");
          final int _cursorIndexOfRouteInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "routeInfo");
          final int _cursorIndexOfSelectedTransportModes = CursorUtil.getColumnIndexOrThrow(_cursor, "selectedTransportModes");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<Schedule> _result = new ArrayList<Schedule>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Schedule _item;
            _item = new Schedule();
            _item.id = _cursor.getInt(_cursorIndexOfId);
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _item.userId = null;
            } else {
              _item.userId = _cursor.getString(_cursorIndexOfUserId);
            }
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _item.title = null;
            } else {
              _item.title = _cursor.getString(_cursorIndexOfTitle);
            }
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _item.date = null;
            } else {
              _item.date = _cursor.getString(_cursorIndexOfDate);
            }
            if (_cursor.isNull(_cursorIndexOfTime)) {
              _item.time = null;
            } else {
              _item.time = _cursor.getString(_cursorIndexOfTime);
            }
            if (_cursor.isNull(_cursorIndexOfDeparture)) {
              _item.departure = null;
            } else {
              _item.departure = _cursor.getString(_cursorIndexOfDeparture);
            }
            if (_cursor.isNull(_cursorIndexOfDestination)) {
              _item.destination = null;
            } else {
              _item.destination = _cursor.getString(_cursorIndexOfDestination);
            }
            if (_cursor.isNull(_cursorIndexOfMemo)) {
              _item.memo = null;
            } else {
              _item.memo = _cursor.getString(_cursorIndexOfMemo);
            }
            if (_cursor.isNull(_cursorIndexOfRouteInfo)) {
              _item.routeInfo = null;
            } else {
              _item.routeInfo = _cursor.getString(_cursorIndexOfRouteInfo);
            }
            if (_cursor.isNull(_cursorIndexOfSelectedTransportModes)) {
              _item.selectedTransportModes = null;
            } else {
              _item.selectedTransportModes = _cursor.getString(_cursorIndexOfSelectedTransportModes);
            }
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _item.isCompleted = _tmp != 0;
            _item.createdAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item.updatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public int getScheduleCountByUserId(final String userId) {
    final String _sql = "SELECT COUNT(*) FROM schedules WHERE userId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (userId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, userId);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if (_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public int getCompletedScheduleCountByUserId(final String userId) {
    final String _sql = "SELECT COUNT(*) FROM schedules WHERE userId = ? AND isCompleted = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (userId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, userId);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if (_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
