<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 비활성화 상태 -->
    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <solid android:color="@color/light_gray" />
            <corners android:radius="@dimen/route_button_corner_radius" />
        </shape>
    </item>
    
    <!-- 눌린 상태 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#0056CC" />
            <corners android:radius="@dimen/route_button_corner_radius" />
        </shape>
    </item>
    
    <!-- 기본 상태 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/route_accent" />
            <corners android:radius="@dimen/route_button_corner_radius" />
        </shape>
    </item>
    
</selector>
