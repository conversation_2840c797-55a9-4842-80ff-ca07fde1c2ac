<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/card_height"
    android:layout_margin="@dimen/card_margin"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground"
    app:cardCornerRadius="@dimen/card_corner_radius"
    app:cardElevation="@dimen/card_elevation"
    app:cardBackgroundColor="@color/card_background"
    app:strokeWidth="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:padding="@dimen/card_padding"
        android:gravity="center_vertical">

        <!-- iOS 스타일 프로필 이미지 -->
        <ImageView
            android:id="@+id/imgProfile"
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:layout_marginEnd="12dp"
            android:background="@drawable/circle_background_ios"
            android:src="@drawable/ic_person"
            android:scaleType="centerCrop"
            android:padding="8dp" />

        <!-- 친구 정보 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginEnd="8dp">

            <TextView
                android:id="@+id/textName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="친구 이름"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:fontFamily="sans-serif-medium"
                android:maxLines="1"
                android:ellipsize="end" />

            <TextView
                android:id="@+id/textUserId"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:text="@user_id"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:fontFamily="sans-serif-light"
                android:maxLines="1"
                android:ellipsize="end" />

        </LinearLayout>

        <!-- iOS 스타일 상태 표시 -->
        <LinearLayout
            android:id="@+id/layoutStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center">

            <!-- 온라인 상태 표시 -->
            <View
                android:id="@+id/viewOnlineStatus"
                android:layout_width="8dp"
                android:layout_height="8dp"
                android:background="@drawable/circle_dot"
                android:backgroundTint="@color/success"
                android:visibility="visible" />

            <!-- 액션 버튼 (친구 요청 시에만 표시) -->
            <LinearLayout
                android:id="@+id/layoutActions"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="4dp"
                android:visibility="gone">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnAccept"
                    android:layout_width="60dp"
                    android:layout_height="32dp"
                    android:layout_marginEnd="4dp"
                    android:text="수락"
                    android:textSize="10sp"
                    android:textColor="@color/white"
                    app:backgroundTint="@color/success"
                    app:cornerRadius="16dp"
                    style="@style/Widget.Material3.Button.UnelevatedButton" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnReject"
                    android:layout_width="60dp"
                    android:layout_height="32dp"
                    android:text="거절"
                    android:textSize="10sp"
                    android:textColor="@color/white"
                    app:backgroundTint="@color/error"
                    app:cornerRadius="16dp"
                    style="@style/Widget.Material3.Button.UnelevatedButton" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
