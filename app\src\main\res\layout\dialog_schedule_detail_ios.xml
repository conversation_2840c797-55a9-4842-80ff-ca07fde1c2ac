<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="20dp"
    app:cardCornerRadius="24dp"
    app:cardElevation="12dp"
    app:cardBackgroundColor="@android:color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="0dp">

        <!-- iOS 스타일 블루톤 헤더 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:background="@color/ios_blue"
            android:padding="24dp"
            android:paddingTop="28dp"
            android:paddingBottom="20dp">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/textScheduleTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="일정 제목"
                    android:textSize="22sp"
                    android:textStyle="bold"
                    android:textColor="@color/white"
                    android:fontFamily="@font/pretendard_bold"
                    android:layout_marginBottom="4dp"
                    android:maxLines="2"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/textScheduleDate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="2024년 1월 15일"
                    android:textSize="16sp"
                    android:textColor="@color/white"
                    android:fontFamily="@font/pretendard_regular"
                    android:alpha="0.9" />

            </LinearLayout>

            <ImageButton
                android:id="@+id/btnClose"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/ic_close"
                android:background="@drawable/ios_circle_button_white"
                android:padding="8dp"
                app:tint="@color/ios_blue" />

        </LinearLayout>

        <!-- iOS 스타일 콘텐츠 영역 -->
        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxHeight="400dp"
            android:padding="24dp"
            android:scrollbars="none"
            android:fadeScrollbars="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- 시간 정보 카드 (아이콘 제거, 블루톤 강조) -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:background="@drawable/ios_card_background"
                    android:padding="20dp"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="일정 시간"
                        android:textSize="14sp"
                        android:textColor="@color/ios_blue"
                        android:fontFamily="@font/pretendard_medium"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/textScheduleTime"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="오후 2:00"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:fontFamily="@font/pretendard_bold" />

                </LinearLayout>

                <!-- 위치 정보 카드 (출발지 + 도착지 통합) -->
                <LinearLayout
                    android:id="@+id/cardLocationInfo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:background="@drawable/ios_card_background"
                    android:padding="20dp"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="위치 정보"
                        android:textSize="14sp"
                        android:textColor="@color/ios_blue"
                        android:fontFamily="@font/pretendard_medium"
                        android:layout_marginBottom="12dp" />

                    <!-- 출발지 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="60dp"
                            android:layout_height="wrap_content"
                            android:text="출발"
                            android:textSize="14sp"
                            android:textColor="@color/text_secondary"
                            android:fontFamily="@font/pretendard_regular" />

                        <TextView
                            android:id="@+id/textDeparture"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="출발지"
                            android:textSize="16sp"
                            android:textColor="@color/text_primary"
                            android:fontFamily="@font/pretendard_medium" />

                    </LinearLayout>

                    <!-- 도착지 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="60dp"
                            android:layout_height="wrap_content"
                            android:text="도착"
                            android:textSize="14sp"
                            android:textColor="@color/text_secondary"
                            android:fontFamily="@font/pretendard_regular" />

                        <TextView
                            android:id="@+id/textDestination"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="도착지"
                            android:textSize="16sp"
                            android:textColor="@color/text_primary"
                            android:fontFamily="@font/pretendard_medium" />

                    </LinearLayout>

                </LinearLayout>

                <!-- 상태 카드 (아이콘 제거) -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:background="@drawable/ios_card_background"
                    android:padding="20dp"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="상태"
                        android:textSize="14sp"
                        android:textColor="@color/ios_blue"
                        android:fontFamily="@font/pretendard_medium"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/textStatus"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="진행중"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary"
                        android:fontFamily="@font/pretendard_medium" />

                </LinearLayout>

                <!-- 메모 카드 (아이콘 제거) -->
                <LinearLayout
                    android:id="@+id/cardMemo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:background="@drawable/ios_card_background"
                    android:padding="20dp"
                    android:layout_marginBottom="16dp"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="메모"
                        android:textSize="14sp"
                        android:textColor="@color/ios_blue"
                        android:fontFamily="@font/pretendard_medium"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/textMemo"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="메모 내용"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary"
                        android:fontFamily="@font/pretendard_medium"
                        android:lineSpacingExtra="4dp" />

                </LinearLayout>

                <!-- 경로 정보 카드 (아이콘 제거) -->
                <LinearLayout
                    android:id="@+id/cardRouteInfo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:background="@drawable/ios_card_background"
                    android:padding="20dp"
                    android:layout_marginBottom="16dp"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="선택된 경로"
                        android:textSize="14sp"
                        android:textColor="@color/ios_blue"
                        android:fontFamily="@font/pretendard_medium"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/textRouteInfo"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="경로 정보"
                        android:textSize="14sp"
                        android:textColor="@color/text_primary"
                        android:fontFamily="@font/pretendard_medium"
                        android:lineSpacingExtra="4dp" />

                </LinearLayout>

                <!-- 함께하는 친구 카드 (아이콘 제거) -->
                <LinearLayout
                    android:id="@+id/cardFriends"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:background="@drawable/ios_card_background"
                    android:padding="20dp"
                    android:layout_marginBottom="16dp"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="함께하는 친구"
                        android:textSize="14sp"
                        android:textColor="@color/ios_blue"
                        android:fontFamily="@font/pretendard_medium"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/textFriends"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="친구 목록"
                        android:textSize="14sp"
                        android:textColor="@color/text_primary"
                        android:fontFamily="@font/pretendard_medium"
                        android:lineSpacingExtra="4dp" />

                </LinearLayout>

            </LinearLayout>

        </ScrollView>

        <!-- iOS 스타일 액션 버튼들 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="24dp"
            android:paddingTop="8dp"
            android:gravity="center">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnEdit"
                android:layout_width="0dp"
                android:layout_height="52dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="수정"
                android:textSize="16sp"
                android:textStyle="bold"
                android:fontFamily="@font/pretendard_bold"
                app:cornerRadius="26dp"
                app:backgroundTint="@color/ios_blue"
                android:textColor="@color/white"
                app:elevation="4dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnDelete"
                android:layout_width="0dp"
                android:layout_height="52dp"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:text="삭제"
                android:textSize="16sp"
                android:textStyle="bold"
                android:fontFamily="@font/pretendard_bold"
                app:cornerRadius="26dp"
                app:backgroundTint="@color/ios_red"
                android:textColor="@color/white"
                app:elevation="4dp" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
