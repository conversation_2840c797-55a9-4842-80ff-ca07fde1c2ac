1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.timemate"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="27"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:6:5-79
12-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
13-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
14-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:8:5-81
14-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:8:22-78
15    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
15-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:9:5-77
15-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:9:22-74
16    <uses-permission android:name="android.permission.VIBRATE" />
16-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:10:5-66
16-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:10:22-63
17    <uses-permission android:name="android.permission.WAKE_LOCK" />
17-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:11:5-68
17-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:11:22-65
18    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
18-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:12:5-81
18-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:12:22-78
19
20    <queries>
20-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:9:5-22:15
21        <package android:name="com.kakao.talk" />
21-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:10:9-50
21-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:10:18-47
22        <package android:name="com.kakao.talk.alpha" />
22-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:11:9-56
22-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:11:18-53
23        <package android:name="com.kakao.talk.sandbox" />
23-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:12:9-58
23-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:12:18-55
24        <package android:name="com.kakao.onetalk" />
24-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:13:9-53
24-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:13:18-50
25
26        <intent>
26-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:15:9-21:18
27            <action android:name="android.intent.action.VIEW" />
27-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:16:13-65
27-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:16:21-62
28
29            <category android:name="android.intent.category.BROWSABLE" />
29-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:18:13-74
29-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:18:23-71
30
31            <data android:scheme="https" />
31-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:20:13-44
31-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:20:19-41
32        </intent>
33    </queries>
34
35    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
35-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
35-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:22-74
36
37    <permission
37-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
38        android:name="com.example.timemate.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
38-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
39        android:protectionLevel="signature" />
39-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
40
41    <uses-permission android:name="com.example.timemate.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
41-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
41-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
42
43    <application
43-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:14:5-67:19
44        android:name="com.example.timemate.TimeMateApplication"
44-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:15:9-44
45        android:allowBackup="true"
45-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:16:9-35
46        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
46-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
47        android:dataExtractionRules="@xml/data_extraction_rules"
47-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:17:9-65
48        android:debuggable="true"
49        android:extractNativeLibs="false"
50        android:fullBackupContent="@xml/backup_rules"
50-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:18:9-54
51        android:icon="@mipmap/ic_launcher"
51-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:19:9-43
52        android:label="@string/app_name"
52-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:20:9-41
53        android:roundIcon="@mipmap/ic_launcher_round"
53-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:21:9-54
54        android:supportsRtl="true"
54-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:22:9-35
55        android:testOnly="true"
56        android:theme="@style/TimeMateTheme"
56-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:23:9-45
57        android:usesCleartextTraffic="true" >
57-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:24:9-44
58
59        <!-- API 키는 BuildConfig를 통해 안전하게 관리됩니다 -->
60        <!-- 메타데이터 방식은 보안상 위험하므로 제거하고 BuildConfig 사용 -->
61
62        <activity
62-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:30:9-37:20
63            android:name="com.example.timemate.MainActivity"
63-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:31:13-41
64            android:exported="true" >
64-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:32:13-36
65            <intent-filter>
65-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:33:13-36:29
66                <action android:name="android.intent.action.MAIN" />
66-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:34:17-69
66-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:34:25-66
67
68                <category android:name="android.intent.category.LAUNCHER" />
68-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:35:17-77
68-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:35:27-74
69            </intent-filter>
70        </activity>
71        <activity android:name="com.example.timemate.SignupFormActivity" />
71-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:39:9-56
71-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:39:19-53
72        <activity android:name="com.example.timemate.ManualLoginActivity" />
72-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:40:9-57
72-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:40:19-54
73        <activity android:name="com.example.timemate.PasswordResetActivity" />
73-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:41:9-59
73-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:41:19-56
74        <activity android:name="com.example.timemate.features.notification.NotificationActivity" />
74-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:42:9-80
74-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:42:19-77
75        <activity android:name="com.example.timemate.ScheduleReminderDetailActivity" />
75-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:43:9-68
75-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:43:19-65
76        <activity android:name="com.example.timemate.AccountSwitchActivity" />
76-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:44:9-59
76-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:44:19-56
77
78        <!-- 새로운 features 패키지 구조의 Activity들 -->
79        <activity android:name="com.example.timemate.features.home.HomeActivity" />
79-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:47:9-64
79-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:47:19-61
80        <activity android:name="com.example.timemate.features.schedule.ScheduleAddActivity" />
80-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:48:9-75
80-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:48:19-72
81        <activity android:name="com.example.timemate.features.schedule.ScheduleListActivity" />
81-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:49:9-76
81-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:49:19-73
82        <activity android:name="com.example.timemate.features.friend.FriendListActivity" />
82-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:50:9-72
82-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:50:19-69
83        <activity android:name="com.example.timemate.features.friend.FriendAddActivity" />
83-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:51:9-71
83-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:51:19-68
84        <activity android:name="com.example.timemate.features.profile.ProfileActivity" />
84-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:52:9-70
84-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:52:19-67
85
86        <!-- 기존 ui 패키지 Activity들도 유지 (호환성) -->
87        <activity android:name="com.example.timemate.ui.home.HomeActivity" />
87-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:55:9-58
87-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:55:19-55
88        <activity android:name="com.example.timemate.ui.schedule.ScheduleListActivity" />
88-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:56:9-70
88-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:56:19-67
89        <activity android:name="com.example.timemate.ui.friend.FriendListActivity" />
89-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:57:9-66
89-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:57:19-63
90        <activity android:name="com.example.timemate.ui.profile.ProfileActivity" />
90-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:58:9-64
90-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:58:19-61
91        <activity android:name="com.example.timemate.ui.recommendation.RecommendationActivity" />
91-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:59:9-78
91-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:59:19-75
92
93        <receiver
93-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:61:9-62:40
94            android:name="com.example.timemate.NotificationActionReceiver"
94-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:61:19-61
95            android:exported="false" />
95-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:62:13-37
96
97        <!-- 일정 알림 Snooze 기능 -->
98        <receiver
98-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:65:9-66:40
99            android:name="com.example.timemate.notification.SnoozeReceiver"
99-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:65:19-62
100            android:exported="false" />
100-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:66:13-37
101
102        <activity
102-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:10:9-15:56
103            android:name="com.kakao.sdk.auth.TalkAuthCodeActivity"
103-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:11:13-67
104            android:configChanges="orientation|screenSize|keyboardHidden"
104-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:12:13-74
105            android:exported="false"
105-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:13:13-37
106            android:launchMode="singleTask"
106-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:14:13-44
107            android:theme="@style/TransparentCompat" />
107-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:15:13-53
108        <activity
108-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:16:9-19:56
109            android:name="com.kakao.sdk.auth.AuthCodeHandlerActivity"
109-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:17:13-70
110            android:launchMode="singleTask"
110-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:18:13-44
111            android:theme="@style/TransparentCompat" />
111-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:19:13-53
112
113        <provider
113-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
114            android:name="androidx.startup.InitializationProvider"
114-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
115            android:authorities="com.example.timemate.androidx-startup"
115-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
116            android:exported="false" >
116-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
117            <meta-data
117-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
118                android:name="androidx.work.WorkManagerInitializer"
118-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
119                android:value="androidx.startup" />
119-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
120            <meta-data
120-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ad3de56ae1ff33cc1a8195de95eedcf\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
121                android:name="androidx.emoji2.text.EmojiCompatInitializer"
121-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ad3de56ae1ff33cc1a8195de95eedcf\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
122                android:value="androidx.startup" />
122-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ad3de56ae1ff33cc1a8195de95eedcf\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
123            <meta-data
123-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50caff7e137a7da8a23bfda87917c629\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
124                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
124-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50caff7e137a7da8a23bfda87917c629\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
125                android:value="androidx.startup" />
125-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50caff7e137a7da8a23bfda87917c629\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
126            <meta-data
126-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
127                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
127-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
128                android:value="androidx.startup" />
128-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
129        </provider>
130
131        <service
131-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
132            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
132-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
133            android:directBootAware="false"
133-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
134            android:enabled="@bool/enable_system_alarm_service_default"
134-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
135            android:exported="false" />
135-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
136        <service
136-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
137            android:name="androidx.work.impl.background.systemjob.SystemJobService"
137-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
138            android:directBootAware="false"
138-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
139            android:enabled="@bool/enable_system_job_service_default"
139-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
140            android:exported="true"
140-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
141            android:permission="android.permission.BIND_JOB_SERVICE" />
141-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
142        <service
142-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
143            android:name="androidx.work.impl.foreground.SystemForegroundService"
143-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
144            android:directBootAware="false"
144-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
145            android:enabled="@bool/enable_system_foreground_service_default"
145-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
146            android:exported="false" />
146-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
147
148        <receiver
148-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
149            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
149-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
150            android:directBootAware="false"
150-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
151            android:enabled="true"
151-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
152            android:exported="false" />
152-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
153        <receiver
153-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
154            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
154-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
155            android:directBootAware="false"
155-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
156            android:enabled="false"
156-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
157            android:exported="false" >
157-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
158            <intent-filter>
158-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
159                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
159-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
159-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
160                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
160-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
160-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
161            </intent-filter>
162        </receiver>
163        <receiver
163-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
164            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
164-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
165            android:directBootAware="false"
165-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
166            android:enabled="false"
166-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
167            android:exported="false" >
167-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
168            <intent-filter>
168-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
169                <action android:name="android.intent.action.BATTERY_OKAY" />
169-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
169-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
170                <action android:name="android.intent.action.BATTERY_LOW" />
170-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
170-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
171            </intent-filter>
172        </receiver>
173        <receiver
173-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
174            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
174-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
175            android:directBootAware="false"
175-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
176            android:enabled="false"
176-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
177            android:exported="false" >
177-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
178            <intent-filter>
178-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
179                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
179-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
179-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
180                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
180-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
180-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
181            </intent-filter>
182        </receiver>
183        <receiver
183-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
184            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
184-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
185            android:directBootAware="false"
185-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
186            android:enabled="false"
186-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
187            android:exported="false" >
187-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
188            <intent-filter>
188-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
189                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
189-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
189-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
190            </intent-filter>
191        </receiver>
192        <receiver
192-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
193            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
193-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
194            android:directBootAware="false"
194-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
195            android:enabled="false"
195-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
196            android:exported="false" >
196-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
197            <intent-filter>
197-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
198                <action android:name="android.intent.action.BOOT_COMPLETED" />
198-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
198-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
199                <action android:name="android.intent.action.TIME_SET" />
199-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
199-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
200                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
200-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
200-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
201            </intent-filter>
202        </receiver>
203        <receiver
203-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
204            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
204-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
205            android:directBootAware="false"
205-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
206            android:enabled="@bool/enable_system_alarm_service_default"
206-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
207            android:exported="false" >
207-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
208            <intent-filter>
208-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
209                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
209-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
209-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
210            </intent-filter>
211        </receiver>
212        <receiver
212-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
213            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
213-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
214            android:directBootAware="false"
214-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
215            android:enabled="true"
215-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
216            android:exported="true"
216-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
217            android:permission="android.permission.DUMP" >
217-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
218            <intent-filter>
218-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
219                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
219-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
219-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
220            </intent-filter>
221        </receiver>
222
223        <service
223-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8239c7620f2b266a50b8bcdfdddedd20\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
224            android:name="androidx.room.MultiInstanceInvalidationService"
224-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8239c7620f2b266a50b8bcdfdddedd20\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
225            android:directBootAware="true"
225-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8239c7620f2b266a50b8bcdfdddedd20\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
226            android:exported="false" />
226-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8239c7620f2b266a50b8bcdfdddedd20\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
227
228        <receiver
228-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
229            android:name="androidx.profileinstaller.ProfileInstallReceiver"
229-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
230            android:directBootAware="false"
230-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
231            android:enabled="true"
231-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
232            android:exported="true"
232-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
233            android:permission="android.permission.DUMP" >
233-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
234            <intent-filter>
234-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
235                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
235-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
235-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
236            </intent-filter>
237            <intent-filter>
237-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
238                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
238-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
238-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
239            </intent-filter>
240            <intent-filter>
240-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
241                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
241-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
241-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
242            </intent-filter>
243            <intent-filter>
243-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
244                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
244-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
244-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
245            </intent-filter>
246        </receiver>
247    </application>
248
249</manifest>
