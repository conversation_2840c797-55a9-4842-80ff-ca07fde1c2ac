<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 눌린 상태 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/light_gray" />
            <corners android:radius="@dimen/route_button_corner_radius" />
            <stroke android:width="1dp" android:color="@color/gray" />
        </shape>
    </item>
    
    <!-- 기본 상태 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/white" />
            <corners android:radius="@dimen/route_button_corner_radius" />
            <stroke android:width="1dp" android:color="@color/light_gray" />
        </shape>
    </item>
    
</selector>
