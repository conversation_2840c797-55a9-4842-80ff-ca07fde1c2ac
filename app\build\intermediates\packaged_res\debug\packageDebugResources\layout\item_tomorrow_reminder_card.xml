<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    android:background="#33B5DFFF">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:background="#33B5DFFF">

        <!-- 제목 -->
        <TextView
            android:id="@+id/textTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="회의 약속"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:layout_marginBottom="8dp" />

        <!-- 날짜·시간 -->
        <TextView
            android:id="@+id/textDateTime"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="2024-01-15 14:00"
            android:textSize="14sp"
            android:textColor="@color/dark_gray"
            android:layout_marginBottom="4dp" />

        <!-- 경로 -->
        <TextView
            android:id="@+id/textRoute"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="집 → 회사"
            android:textSize="14sp"
            android:textColor="@color/dark_gray"
            android:layout_marginBottom="8dp" />

        <!-- 소요시간 -->
        <TextView
            android:id="@+id/textDuration"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="예상 25분 소요"
            android:textSize="14sp"
            android:textColor="@color/dark_gray"
            android:layout_marginBottom="4dp" />

        <!-- 추천 출발시간 (강조) -->
        <TextView
            android:id="@+id/textDepartureTime"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="추천 출발: 13:25"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#FF6F91"
            android:layout_marginBottom="4dp" />

        <!-- 교통수단 -->
        <TextView
            android:id="@+id/textTransport"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="🚇 지하철"
            android:textSize="14sp"
            android:textColor="@color/dark_gray"
            android:layout_marginBottom="12dp" />

        <!-- 버튼 영역 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">

            <Button
                android:id="@+id/btnViewDetail"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="상세보기"
                android:textSize="12sp"
                android:background="@drawable/button_outline"
                android:textColor="@color/primary_color"
                android:layout_marginEnd="8dp"
                android:minWidth="0dp"
                android:paddingHorizontal="16dp" />

            <Button
                android:id="@+id/btnConfirm"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="확인"
                android:textSize="12sp"
                android:background="@drawable/button_primary"
                android:textColor="@color/white"
                android:minWidth="0dp"
                android:paddingHorizontal="16dp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
