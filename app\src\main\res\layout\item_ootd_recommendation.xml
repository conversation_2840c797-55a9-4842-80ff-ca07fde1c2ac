<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="280dp"
    android:layout_height="320dp"
    android:layout_marginEnd="16dp"
    app:cardCornerRadius="20dp"
    app:cardElevation="8dp"
    app:cardBackgroundColor="@android:color/white"
    app:strokeWidth="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- 이미지 영역 -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="180dp">

            <ImageView
                android:id="@+id/imgOOTD"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:src="@android:drawable/ic_menu_gallery"
                android:background="@color/background_secondary" />

            <!-- 카테고리 태그 -->
            <TextView
                android:id="@+id/textOOTDCategory"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="top|end"
                android:layout_margin="12dp"
                android:background="@drawable/bg_tag_rounded"
                android:paddingHorizontal="12dp"
                android:paddingVertical="6dp"
                android:text="👗 스타일"
                android:textColor="@android:color/white"
                android:textSize="12sp"
                android:textStyle="bold" />

        </FrameLayout>

        <!-- 내용 영역 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 제목 -->
            <TextView
                android:id="@+id/textOOTDTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="스타일 제목"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:fontFamily="sans-serif-medium"
                android:maxLines="2"
                android:ellipsize="end"
                android:layout_marginBottom="8dp" />

            <!-- 설명 -->
            <TextView
                android:id="@+id/textOOTDDescription"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:text="스타일 설명이 여기에 표시됩니다"
                android:textSize="13sp"
                android:textColor="@color/text_secondary"
                android:lineSpacingExtra="2dp"
                android:maxLines="3"
                android:ellipsize="end"
                android:layout_marginBottom="8dp" />

            <!-- 태그 -->
            <TextView
                android:id="@+id/textOOTDTags"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="#트렌디 #편안함 #데일리"
                android:textSize="11sp"
                android:textColor="@color/ios_blue"
                android:maxLines="1"
                android:ellipsize="end"
                android:visibility="gone" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
