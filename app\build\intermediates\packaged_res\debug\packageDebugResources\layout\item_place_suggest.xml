<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="12dp"
    android:background="?android:attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <!-- 위치 아이콘 -->
        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/ic_location"
            android:layout_marginEnd="8dp"
            app:tint="@color/sky_blue_accent" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- 장소명 -->
            <TextView
                android:id="@+id/textPlaceName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="장소명"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:maxLines="1"
                android:ellipsize="end" />

            <!-- 주소 -->
            <TextView
                android:id="@+id/textPlaceAddress"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="주소"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:layout_marginTop="2dp"
                android:maxLines="1"
                android:ellipsize="end" />

        </LinearLayout>

        <!-- 카테고리 -->
        <TextView
            android:id="@+id/textPlaceCategory"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="카테고리"
            android:textSize="12sp"
            android:textColor="@color/sky_blue_accent"
            android:background="@drawable/category_tag_background"
            android:padding="4dp"
            android:layout_marginStart="8dp"
            android:visibility="gone" />

    </LinearLayout>

    <!-- 구분선 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/divider"
        android:layout_marginTop="8dp"
        android:alpha="0.3" />

</LinearLayout>
