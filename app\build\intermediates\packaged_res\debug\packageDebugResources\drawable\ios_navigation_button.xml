<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 눌린 상태 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/ios_blue_light" />
            <corners android:radius="16dp" />
            <stroke android:width="1dp" android:color="@color/ios_blue" />
        </shape>
    </item>
    
    <!-- 기본 상태 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <corners android:radius="16dp" />
            <stroke android:width="1dp" android:color="@color/ios_blue" />
        </shape>
    </item>
    
</selector>
