<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_secondary">

    <!-- 스크롤 가능한 컨텐츠 영역 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- iOS 스타일 헤더 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                style="@style/TimeMateAppBar"
                android:orientation="vertical"
                android:paddingTop="8dp">

                <!-- 상단 바 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="8dp"
                    android:layout_marginTop="40dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="TimeMate"
                        style="@style/iOSTitle"
                        android:textColor="@color/text_primary" />

                    <!-- 알림 버튼 (배지 포함) -->
                    <FrameLayout
                        android:layout_width="44dp"
                        android:layout_height="44dp">

                        <ImageButton
                            android:id="@+id/btnNotifications"
                            android:layout_width="44dp"
                            android:layout_height="44dp"
                            android:src="@drawable/ic_notifications"
                            android:background="@drawable/ios_button_background"
                            app:tint="@color/text_primary" />

                        <!-- 알림 배지 -->
                        <TextView
                            android:id="@+id/textNotificationBadge"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="top|end"
                            android:layout_marginTop="2dp"
                            android:layout_marginEnd="2dp"
                            android:background="@drawable/notification_badge_background"
                            android:gravity="center"
                            android:text="0"
                            android:textColor="@android:color/white"
                            android:textSize="10sp"
                            android:textStyle="bold"
                            android:visibility="gone" />

                    </FrameLayout>

                </LinearLayout>

                <!-- iOS 스타일 날씨 정보 섹션 -->
                <LinearLayout
                    android:id="@+id/layoutWeather"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="20dp"
                    android:layout_marginTop="12dp"
                    android:background="@drawable/ios_card_background"
                    android:orientation="horizontal"
                    android:padding="20dp"
                    android:gravity="center_vertical"
                    android:clickable="true"
                    android:focusable="true">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical">

                            <TextView
                                android:id="@+id/textTemperature"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="--°"
                                style="@style/TimeMateTextTemperature" />

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:layout_marginStart="12dp">

                                <TextView
                                    android:id="@+id/textCityName"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="위치 확인 중..."
                                    style="@style/iOSSubheadline"
                                    android:textColor="@color/text_primary" />

                                <TextView
                                    android:id="@+id/textWeatherDescription"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="날씨 정보 로딩 중..."
                                    style="@style/iOSCallout" />

                            </LinearLayout>

                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:gravity="end">

                        <TextView
                            android:id="@+id/textFeelsLike"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="체감 --°"
                            style="@style/iOSFootnote" />

                        <TextView
                            android:id="@+id/textHumidity"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="습도 --%"
                            style="@style/iOSFootnote"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

            <!-- 오늘 일정 박스 -->
            <LinearLayout
                android:id="@+id/layoutTodayScheduleBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="20dp"
                android:layout_marginTop="24dp"
                android:background="@drawable/ios_card_background"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- 헤더 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="오늘의 일정"
                        style="@style/iOSHeadline"
                        android:textColor="@color/text_primary" />

                    <View
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:layout_weight="1" />

                    <TextView
                        android:id="@+id/textTodayDate"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="12월 25일 (수)"
                        style="@style/iOSCallout"
                        android:textColor="@color/text_secondary" />

                </LinearLayout>

                <!-- 오늘 일정 리스트 -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerTodayScheduleBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false"
                    android:layout_marginBottom="8dp" />

                <!-- 일정 없을 때 메시지 -->
                <TextView
                    android:id="@+id/textNoScheduleToday"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="오늘 예정된 일정이 없습니다 ✨"
                    style="@style/iOSCallout"
                    android:textColor="@color/text_secondary"
                    android:gravity="center"
                    android:padding="16dp"
                    android:visibility="gone" />

            </LinearLayout>

            <!-- iOS 스타일 빠른 액션 카드들 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="20dp"
                android:layout_marginTop="16dp"
                android:orientation="horizontal">

                <!-- 일정 추가 카드 -->
                <LinearLayout
                    android:id="@+id/btnQuickAddSchedule"
                    android:layout_width="0dp"
                    android:layout_height="100dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="10dp"
                    android:background="@drawable/ios_card_background"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="16dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?android:attr/selectableItemBackground">

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@drawable/ic_add"
                        app:tint="@color/pastel_pink"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="일정 추가"
                        style="@style/iOSCallout"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

                <!-- 일정 보기 카드 -->
                <LinearLayout
                    android:id="@+id/btnViewAllSchedules"
                    android:layout_width="0dp"
                    android:layout_height="100dp"
                    android:layout_weight="1"
                    android:layout_marginStart="10dp"
                    android:background="@drawable/ios_card_background"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="16dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?android:attr/selectableItemBackground">

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@drawable/ic_calendar"
                        app:tint="@color/pastel_pink"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="일정 보기"
                        style="@style/iOSCallout"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

            </LinearLayout>

            <!-- 오늘 일정 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerTodaySchedule"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="20dp"
                android:layout_marginTop="20dp"
                android:visibility="gone" />

            <!-- 내일 일정 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerTomorrowSchedule"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="20dp"
                android:layout_marginTop="10dp"
                android:visibility="gone" />

            <!-- 내일 일정 미리보기 카드 -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cardTomorrowReminder"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="20dp"
                android:layout_marginTop="20dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/ios_card_background"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="12dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_time"
                            app:tint="@color/ios_blue"
                            android:layout_marginEnd="8dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="내일 일정 미리보기"
                            style="@style/TimeMateTextSubtitle"
                            android:textColor="@color/text_primary" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/textTomorrowTitle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="회의 참석"
                        style="@style/TimeMateTextTitle"
                        android:textColor="@color/ios_blue"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/textTomorrowRoute"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="집 → 회사"
                        style="@style/TimeMateTextBody"
                        android:textColor="@color/text_secondary"
                        android:layout_marginBottom="8dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/textTomorrowDuration"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="시간: 09:00"
                            style="@style/TimeMateTextCaption"
                            android:textColor="@color/text_secondary" />

                        <TextView
                            android:id="@+id/textTomorrowDeparture"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="내일 일정"
                            style="@style/TimeMateTextCaption"
                            android:textColor="@color/ios_green"
                            android:gravity="end" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 날씨 기반 OOTD 추천 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="20dp"
                android:layout_marginTop="20dp"
                android:background="@drawable/ios_card_background"
                android:orientation="vertical"
                android:padding="20dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_weather"
                        app:tint="@color/ios_orange"
                        android:layout_marginEnd="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="오늘의 OOTD 추천"
                        style="@style/TimeMateTextSubtitle"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

                <TextView
                    android:id="@+id/textOotdDescription"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="현재 날씨에 맞는 옷차림을 추천해드려요"
                    style="@style/TimeMateTextCaption"
                    android:textColor="@color/text_secondary"
                    android:layout_marginBottom="16dp" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerOotd"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

    <!-- iOS 스타일 바텀 네비게이션 -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottomNavigationView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        style="@style/TimeMateBottomNavigation"
        app:itemIconTint="@color/bottom_nav_color"
        app:itemTextColor="@color/bottom_nav_color"
        app:menu="@menu/bottom_nav_menu" />

</LinearLayout>
