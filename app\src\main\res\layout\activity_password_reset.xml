<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp"
        android:gravity="center">

        <!-- 헤더 영역 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginTop="40dp"
            android:layout_marginBottom="32dp">

            <!-- 아이콘 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="🔐"
                android:textSize="48sp"
                android:layout_marginBottom="16dp" />

            <!-- 제목 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="비밀번호 찾기"
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="8dp" />

            <!-- 설명 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="아이디를 입력하여 비밀번호를 재설정하세요"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:gravity="center" />

        </LinearLayout>

        <!-- 입력 영역 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="24dp">

            <!-- 아이디 입력 -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="아이디"
                android:layout_marginBottom="16dp"
                style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/editUserId"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 새 비밀번호 입력 (초기에는 숨김) -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/layoutNewPassword"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="새 비밀번호"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/editNewPassword"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPassword"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 새 비밀번호 확인 (초기에는 숨김) -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/layoutConfirmPassword"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="새 비밀번호 확인"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/editConfirmPassword"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPassword"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

        <!-- 사용자 정보 표시 영역 (초기에는 숨김) -->
        <LinearLayout
            android:id="@+id/layoutUserInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/ios_card_background"
            android:padding="16dp"
            android:layout_marginBottom="24dp"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="📋 사용자 정보 확인"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/textUserInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:lineSpacingExtra="4dp" />

        </LinearLayout>

        <!-- 버튼 영역 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <!-- 취소 버튼 -->
            <Button
                android:id="@+id/btnCancel"
                android:text="취소"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:textColor="@color/text_secondary" />

            <!-- 확인/변경 버튼 -->
            <Button
                android:id="@+id/btnConfirm"
                android:text="사용자 확인"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
