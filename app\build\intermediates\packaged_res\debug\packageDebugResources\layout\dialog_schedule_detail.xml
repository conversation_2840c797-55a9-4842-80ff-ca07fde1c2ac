<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/ios_card_background"
    android:padding="20dp">

    <!-- 헤더 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <TextView
            android:id="@+id/textDialogDate"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="2024년 1월 15일"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary" />

        <ImageButton
            android:id="@+id/btnCloseDialog"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_close"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            app:tint="@color/text_secondary" />

    </LinearLayout>

    <!-- 일정 목록 -->
    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPagerSchedules"
        android:layout_width="match_parent"
        android:layout_height="300dp"
        android:layout_marginBottom="16dp" />

    <!-- 페이지 인디케이터 -->
    <LinearLayout
        android:id="@+id/layoutIndicator"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginBottom="16dp"
        android:visibility="gone">

        <!-- 동적으로 점들이 추가됨 -->

    </LinearLayout>

    <!-- 하단 버튼들 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btnAddSchedule"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="일정 추가"
            android:layout_marginEnd="8dp"
            style="@style/Widget.Material3.Button.OutlinedButton" />

        <Button
            android:id="@+id/btnViewAll"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="전체 보기"
            android:layout_marginStart="8dp"
            android:backgroundTint="@color/ios_blue" />

    </LinearLayout>

</LinearLayout>
