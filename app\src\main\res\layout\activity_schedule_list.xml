<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- 프로필과 같은 헤더 스타일 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:paddingTop="56dp"
            android:gravity="center_vertical"
            android:background="@android:color/white"
            android:elevation="2dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="일정 관리"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:fontFamily="@font/pretendard_bold"
                android:gravity="center" />

        </LinearLayout>

        <!-- iOS 스타일 캘린더 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:background="@drawable/ios_card_background"
            android:orientation="vertical"
            android:padding="20dp">

            <!-- 캘린더 헤더 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <Button
                    android:id="@+id/btnPrevMonth"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:text="‹"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/accent"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_marginEnd="16dp" />

                <TextView
                    android:id="@+id/textCurrentMonth"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="2024년 1월"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:gravity="center" />

                <Button
                    android:id="@+id/btnNextMonth"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:text="›"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/accent"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_marginStart="16dp" />

            </LinearLayout>

            <!-- 캘린더 뷰 -->
            <com.example.timemate.features.home.CalendarView
                android:id="@+id/calendarView"
                android:layout_width="match_parent"
                android:layout_height="300dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="• 일정이 있는 날짜를 터치하면 상세 정보를 볼 수 있습니다"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:layout_marginTop="12dp" />

        </LinearLayout>

        <!-- 일정 리스트 컨테이너 -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <!-- 일정 리스트 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerSchedules"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="16dp"
                android:clipToPadding="false" />

            <!-- 💙 블루 톤 Empty State -->
            <LinearLayout
                android:id="@+id/layoutEmptyState"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="32dp"
                android:visibility="gone">



                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="등록된 일정이 없습니다"
                    android:textSize="18sp"
                    android:textColor="@color/text_primary"
                    android:textStyle="bold"
                    android:fontFamily="sans-serif-medium"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/textEmptySchedule"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="+ 버튼을 눌러 일정을 추가해보세요"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:fontFamily="sans-serif-light" />

            </LinearLayout>

        </FrameLayout>

        <!-- 바텀 네비게이션 -->
        <com.google.android.material.bottomnavigation.BottomNavigationView
            android:id="@+id/bottomNavigationView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            style="@style/TimeMateBottomNavigation"
            app:itemIconTint="@color/bottom_nav_color"
            app:itemTextColor="@color/bottom_nav_color"
            app:menu="@menu/bottom_nav_menu" />

    </LinearLayout>

    <!-- iOS 스타일 FAB 일정 추가 버튼 (위치 조정: 위로 올리고 왼쪽으로) -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabAddSchedule"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginEnd="24dp"
        android:layout_marginBottom="100dp"
        android:src="@drawable/ic_add"
        android:contentDescription="일정 추가"
        app:backgroundTint="@color/accent"
        app:tint="@color/text_on_primary"
        app:elevation="@dimen/card_elevation_selected" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>