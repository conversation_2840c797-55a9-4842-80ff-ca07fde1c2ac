<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="4dp"
    android:layout_marginBottom="12dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="2dp"
    app:cardBackgroundColor="@android:color/white"
    android:clickable="true"
    android:focusable="true"
    app:rippleColor="@color/ios_blue_light">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp">

        <!-- iOS 스타일 알림 헤더 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

            <ImageView
                android:id="@+id/iconNotification"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_notifications"
                app:tint="@color/ios_blue"
                android:layout_marginEnd="16dp"
                android:background="@drawable/ios_icon_background"
                android:padding="6dp" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/textNotificationTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="📅 일정 초대"
                    style="@style/iOSHeadline"
                    android:textColor="@color/text_primary" />

                <TextView
                    android:id="@+id/textNotificationTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="방금 전"
                    style="@style/iOSCaption"
                    android:textColor="@color/text_secondary"
                    android:layout_marginTop="4dp" />

            </LinearLayout>

        </LinearLayout>

        <!-- iOS 스타일 알림 메시지 -->
        <TextView
            android:id="@+id/textNotificationMessage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="친구님이 일정에 초대했습니다"
            style="@style/iOSBody"
            android:textColor="@color/text_primary"
            android:layout_marginBottom="16dp"
            android:lineSpacingExtra="4dp" />

        <!-- iOS 스타일 액션 버튼들 -->
        <LinearLayout
            android:id="@+id/layoutActions"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnReject"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_marginEnd="12dp"
                android:text="거절"
                android:textSize="14sp"
                android:textStyle="bold"
                app:cornerRadius="20dp"
                style="@style/Widget.Material3.Button.TextButton"
                android:textColor="@color/text_secondary"
                android:minWidth="88dp"
                app:strokeColor="@color/light_gray"
                app:strokeWidth="1dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnAccept"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:text="수락"
                android:textSize="14sp"
                android:textStyle="bold"
                app:cornerRadius="20dp"
                app:backgroundTint="@color/ios_blue"
                android:textColor="@android:color/white"
                android:minWidth="88dp"
                android:elevation="2dp" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
