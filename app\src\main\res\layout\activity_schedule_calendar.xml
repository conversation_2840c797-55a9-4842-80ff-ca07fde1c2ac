<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    tools:context=".features.schedule.ScheduleCalendarActivity">

    <!-- 메인 콘텐츠 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="80dp"
        android:orientation="vertical">

        <!-- 상단 앱바 -->
        <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/sky_blue_primary"
            app:elevation="4dp">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="@color/sky_blue_primary"
                app:title="일정 달력"
                app:titleTextColor="@color/white"
                app:titleTextAppearance="@style/TextAppearance.AppCompat.Title" />

        </com.google.android.material.appbar.AppBarLayout>

        <!-- 달력 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:strokeWidth="0dp">

            <CalendarView
                android:id="@+id/calendarView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="8dp"
                android:dateTextAppearance="@style/CalendarDateTextAppearance"
                android:weekDayTextAppearance="@style/CalendarWeekDayTextAppearance" />

        </com.google.android.material.card.MaterialCardView>

        <!-- 선택된 날짜의 일정 제목 -->
        <TextView
            android:id="@+id/textSelectedDateTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:text="오늘의 일정"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary" />

        <!-- 일정 목록 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerSchedules"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:layout_marginHorizontal="16dp"
            android:clipToPadding="false"
            android:paddingBottom="16dp"
            tools:listitem="@layout/item_schedule" />

        <!-- 일정이 없을 때 표시할 메시지 -->
        <LinearLayout
            android:id="@+id/layoutEmptyState"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone">



            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="선택한 날짜에 일정이 없습니다"
                android:textColor="@color/text_secondary"
                android:textSize="16sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="+ 버튼을 눌러 새 일정을 추가해보세요"
                android:textColor="@color/text_hint"
                android:textSize="14sp" />

        </LinearLayout>

    </LinearLayout>

    <!-- 플로팅 액션 버튼 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabAddSchedule"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:layout_marginBottom="96dp"
        android:src="@android:drawable/ic_input_add"
        android:contentDescription="일정 추가"
        app:backgroundTint="@color/sky_blue_primary"
        app:tint="@color/white"
        app:elevation="6dp"
        app:pressedTranslationZ="12dp" />

    <!-- 하단 네비게이션 -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottomNavigationView"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:layout_gravity="bottom"
        android:background="@color/white"
        app:elevation="8dp"
        app:itemIconTint="@color/bottom_nav_color"
        app:itemTextColor="@color/bottom_nav_color"
        app:labelVisibilityMode="labeled"
        app:menu="@menu/bottom_nav_menu" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
