com.example.timemate.app-sqlite-release-0 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00e9749df20cc7456761364a622721a6\transformed\sqlite-release\res
com.example.timemate.app-annotation-experimental-1.4.1-1 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01b75e4effa51b703bfc1a44c215e615\transformed\annotation-experimental-1.4.1\res
com.example.timemate.app-browser-1.4.0-2 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02344620b027cd4b51022a2f63eb19e2\transformed\browser-1.4.0\res
com.example.timemate.app-lifecycle-livedata-2.6.2-3 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\03aec2c1b2d4b822f2f6d93162037d6b\transformed\lifecycle-livedata-2.6.2\res
com.example.timemate.app-lifecycle-livedata-core-2.6.2-4 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09d9eb7809036a872ff86964716af4a2\transformed\lifecycle-livedata-core-2.6.2\res
com.example.timemate.app-core-runtime-2.2.0-5 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d75dade586afbe1d1c221fdf6849f78\transformed\core-runtime-2.2.0\res
com.example.timemate.app-activity-1.10.1-6 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17a30490300b033b1772538f2657fe99\transformed\activity-1.10.1\res
com.example.timemate.app-core-1.13.0-7 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\res
com.example.timemate.app-lifecycle-service-2.6.2-8 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\265fe3f3360174eccaaaa0945467fbbe\transformed\lifecycle-service-2.6.2\res
com.example.timemate.app-emoji2-views-helper-1.3.0-9 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c4edc17630951cf74586009cd1dd1c1\transformed\emoji2-views-helper-1.3.0\res
com.example.timemate.app-savedstate-1.2.1-10 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d4c630fd0951313ea28adde522c87ae\transformed\savedstate-1.2.1\res
com.example.timemate.app-appcompat-1.7.1-11 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d4d96d74a14046b91d40cca3b0165b0\transformed\appcompat-1.7.1\res
com.example.timemate.app-material-1.12.0-12 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3104310070aa521e0a811bb63bfe0177\transformed\material-1.12.0\res
com.example.timemate.app-lifecycle-viewmodel-savedstate-2.6.2-13 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36d11ddaf7526eb0ddf91602b42d8070\transformed\lifecycle-viewmodel-savedstate-2.6.2\res
com.example.timemate.app-cardview-1.0.0-14 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b273577ec1a001e95b5541801b37991\transformed\cardview-1.0.0\res
com.example.timemate.app-v2-common-2.19.0-15 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\res
com.example.timemate.app-lifecycle-process-2.6.2-16 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50caff7e137a7da8a23bfda87917c629\transformed\lifecycle-process-2.6.2\res
com.example.timemate.app-profileinstaller-1.4.0-17 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\res
com.example.timemate.app-constraintlayout-2.2.1-18 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\560962a121f6ed5b86b69c6ee9329d69\transformed\constraintlayout-2.2.1\res
com.example.timemate.app-transition-1.5.0-19 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\630c6c3476789001e5d078c8dc912e22\transformed\transition-1.5.0\res
com.example.timemate.app-glide-4.16.0-20 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d862bf2b5a42c7d4dba2051d7d69072\transformed\glide-4.16.0\res
com.example.timemate.app-lifecycle-viewmodel-2.6.2-21 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\712a6bbecbfb44cfdd41df523efdfe30\transformed\lifecycle-viewmodel-2.6.2\res
com.example.timemate.app-core-viewtree-1.0.0-22 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7aa451b2c9b6d3bd6b8a8a0c5eab5d86\transformed\core-viewtree-1.0.0\res
com.example.timemate.app-room-runtime-release-23 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8239c7620f2b266a50b8bcdfdddedd20\transformed\room-runtime-release\res
com.example.timemate.app-fragment-1.5.4-24 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\844f5d9328c7c028844d43f11e1bb1f5\transformed\fragment-1.5.4\res
com.example.timemate.app-recyclerview-1.1.0-25 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85db21db2d396b7cfb1ef0739871e833\transformed\recyclerview-1.1.0\res
com.example.timemate.app-work-runtime-2.8.1-26 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\res
com.example.timemate.app-emoji2-1.3.0-27 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ad3de56ae1ff33cc1a8195de95eedcf\transformed\emoji2-1.3.0\res
com.example.timemate.app-drawerlayout-1.1.1-28 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a59370747b5888872782a6dae5f375a9\transformed\drawerlayout-1.1.1\res
com.example.timemate.app-startup-runtime-1.1.1-29 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6ac2588e2144b2e16f39927e0dc770d\transformed\startup-runtime-1.1.1\res
com.example.timemate.app-sqlite-framework-release-30 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ada30ec73165fa5df5a5a5b588341cde\transformed\sqlite-framework-release\res
com.example.timemate.app-v2-auth-2.19.0-31 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\res
com.example.timemate.app-lifecycle-runtime-2.6.2-32 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4741cf45e1eee184090d5422ee18388\transformed\lifecycle-runtime-2.6.2\res
com.example.timemate.app-core-ktx-1.13.0-33 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda92deac3742e21d61083e50a8ea4bf\transformed\core-ktx-1.13.0\res
com.example.timemate.app-coordinatorlayout-1.1.0-34 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dcccced35f654e81232cf08c4afbe3a0\transformed\coordinatorlayout-1.1.0\res
com.example.timemate.app-appcompat-resources-1.7.1-35 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed874117c9c65f406b3b8e55fc39afa3\transformed\appcompat-resources-1.7.1\res
com.example.timemate.app-viewpager2-1.0.0-36 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f77804f9fc0a0391ec02c7b7fef452cc\transformed\viewpager2-1.0.0\res
com.example.timemate.app-pngs-37 C:\Users\<USER>\TimeMate\app\build\generated\res\pngs\debug
com.example.timemate.app-resValues-38 C:\Users\<USER>\TimeMate\app\build\generated\res\resValues\debug
com.example.timemate.app-packageDebugResources-39 C:\Users\<USER>\TimeMate\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.timemate.app-packageDebugResources-40 C:\Users\<USER>\TimeMate\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.timemate.app-debug-41 C:\Users\<USER>\TimeMate\app\build\intermediates\merged_res\debug\mergeDebugResources
com.example.timemate.app-debug-42 C:\Users\<USER>\TimeMate\app\src\debug\res
com.example.timemate.app-main-43 C:\Users\<USER>\TimeMate\app\src\main\res
