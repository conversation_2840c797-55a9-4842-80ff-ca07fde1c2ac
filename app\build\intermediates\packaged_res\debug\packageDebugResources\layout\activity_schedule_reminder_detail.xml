<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light">

    <!-- 헤더 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/sky_blue_accent"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <ImageButton
            android:id="@+id/btnBack"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_arrow_back"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            app:tint="@android:color/white" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="일정 알림"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@android:color/white"
            android:gravity="center" />

        <View
            android:layout_width="48dp"
            android:layout_height="48dp" />

    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="20dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 일정 정보 카드 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/pastel_lavender"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:id="@+id/textTitle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="일정 제목"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:gravity="center"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:id="@+id/textDateTime"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="2024년 1월 1일 10:00"
                        android:textSize="18sp"
                        android:textColor="@color/text_secondary"
                        android:gravity="center"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/textRoute"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="출발지 → 도착지"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary"
                        android:gravity="center" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 출발 정보 카드 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/pastel_mint"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="🚗 추천 출발 정보"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="16dp" />

                    <TextView
                        android:id="@+id/textDepartureTime"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="추천 출발시간: 09:30"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="@color/sky_blue_accent"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/textDuration"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="예상 30분 소요"
                        android:textSize="16sp"
                        android:textColor="@color/text_secondary"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/textTransport"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="교통수단: 자동차"
                        android:textSize="16sp"
                        android:textColor="@color/text_secondary"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/textDistance"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="거리: 15.2 km"
                        android:textSize="16sp"
                        android:textColor="@color/text_secondary" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 비용 정보 카드 (자동차인 경우만) -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/pastel_yellow"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="💰 예상 비용"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="12dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/textTollFare"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="통행료: 2,500원"
                            android:textSize="16sp"
                            android:textColor="@color/text_secondary" />

                        <TextView
                            android:id="@+id/textFuelPrice"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="연료비: 3,200원"
                            android:textSize="16sp"
                            android:textColor="@color/text_secondary" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </ScrollView>

    <!-- 하단 액션 버튼들 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp"
        android:background="@color/card_background"
        android:elevation="8dp">

        <!-- 길찾기 시작 버튼 -->
        <Button
            android:id="@+id/btnStartNavigation"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="🗺️ 길찾기 시작"
            android:textSize="18sp"
            android:textStyle="bold"
            android:backgroundTint="@color/sky_blue_accent"
            android:layout_marginBottom="12dp"
            style="@style/Widget.Material3.Button" />

        <!-- 스누즈/해제 버튼 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btnSnooze"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:text="⏰ 10분 후"
                android:textSize="14sp"
                android:backgroundTint="@color/orange"
                android:layout_marginEnd="8dp"
                style="@style/Widget.Material3.Button" />

            <Button
                android:id="@+id/btnDismiss"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:text="✓ 확인"
                android:textSize="14sp"
                android:backgroundTint="@color/text_hint"
                android:layout_marginStart="8dp"
                style="@style/Widget.Material3.Button" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
