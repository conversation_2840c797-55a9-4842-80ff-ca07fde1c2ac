{"formatVersion": 1, "database": {"version": 3, "identityHash": "f8971c9bed2cda77ce03f820009d557e", "entities": [{"tableName": "user", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userId` TEXT NOT NULL, `nickname` TEXT, `profileImage` TEXT, `email` TEXT, `gender` TEXT, `phone` TEXT, `password` TEXT, PRIMARY KEY(`userId`))", "fields": [{"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "nickname", "columnName": "nickname", "affinity": "TEXT", "notNull": false}, {"fieldPath": "profileImage", "columnName": "profileImage", "affinity": "TEXT", "notNull": false}, {"fieldPath": "email", "columnName": "email", "affinity": "TEXT", "notNull": false}, {"fieldPath": "gender", "columnName": "gender", "affinity": "TEXT", "notNull": false}, {"fieldPath": "phone", "columnName": "phone", "affinity": "TEXT", "notNull": false}, {"fieldPath": "password", "columnName": "password", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["userId"]}, "indices": [], "foreignKeys": []}, {"tableName": "friend", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`friendId` TEXT NOT NULL, `user_id` TEXT, `friend_nickname` TEXT, PRIMARY KEY(`friendId`))", "fields": [{"fieldPath": "friendId", "columnName": "friendId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "user_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "friend<PERSON><PERSON><PERSON>", "columnName": "friend_nickname", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["friendId"]}, "indices": [], "foreignKeys": []}, {"tableName": "schedule", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `title` TEXT, `date_time` TEXT, `departure` TEXT, `destination` TEXT, `memo` TEXT)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": false}, {"fieldPath": "dateTime", "columnName": "date_time", "affinity": "TEXT", "notNull": false}, {"fieldPath": "departure", "columnName": "departure", "affinity": "TEXT", "notNull": false}, {"fieldPath": "destination", "columnName": "destination", "affinity": "TEXT", "notNull": false}, {"fieldPath": "memo", "columnName": "memo", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "Participant", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`scheduleId` INTEGER NOT NULL, `userId` TEXT NOT NULL, `isAccepted` INTEGER NOT NULL, PRIMARY KEY(`scheduleId`, `userId`))", "fields": [{"fieldPath": "scheduleId", "columnName": "scheduleId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isAccepted", "columnName": "isAccepted", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["scheduleId", "userId"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'f8971c9bed2cda77ce03f820009d557e')"]}}