<!-- res/layout/activity_signup.xml -->
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="24dp">

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <EditText
            android:id="@+id/editNickname"
            android:hint="이름"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <EditText
            android:id="@+id/editEmail"
            android:hint="이메일"
            android:inputType="textEmailAddress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <EditText
            android:id="@+id/editPhone"
            android:hint="전화번호"
            android:inputType="phone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <RadioGroup
            android:id="@+id/radioGenderGroup"
            android:orientation="horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <RadioButton
                android:id="@+id/radioMale"
                android:text="남"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <RadioButton
                android:id="@+id/radioFemale"
                android:text="여"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
        </RadioGroup>

        <EditText
            android:id="@+id/editUserId"
            android:hint="로그인 ID"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <EditText
            android:id="@+id/editPassword"
            android:hint="비밀번호"
            android:inputType="textPassword"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <!-- 버튼 영역 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="16dp">

            <!-- 취소 버튼 -->
            <Button
                android:id="@+id/btnCancel"
                android:text="취소"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:textColor="@color/text_secondary" />

            <!-- 회원가입 버튼 -->
            <Button
                android:id="@+id/btnSignup"
                android:text="회원가입"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp" />

        </LinearLayout>
    </LinearLayout>
</ScrollView>
