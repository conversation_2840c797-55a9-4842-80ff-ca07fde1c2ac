package com.example.timemate.features.notification;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.ImageButton;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.timemate.R;
import com.example.timemate.adapters.NotificationAdapter;
import com.example.timemate.data.database.AppDatabase;
import com.example.timemate.data.model.SharedSchedule;
import com.example.timemate.core.util.UserSession;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;

/**
 * 알림 목록 화면
 * - 친구 초대 알림
 * - 일정 공유 요청
 * - 시스템 알림
 */
public class NotificationActivity extends AppCompatActivity {
    
    private static final String TAG = "NotificationActivity";
    
    private RecyclerView recyclerNotifications;
    private TextView textEmptyNotifications;
    private TextView btnTabPending;
    private TextView btnTabAll;
    private NotificationAdapter adapter;
    private UserSession userSession;
    private List<SharedSchedule> notificationList;
    private List<SharedSchedule> allNotifications; // 전체 알림 목록

    // 탭 상태
    private boolean isShowingPendingOnly = true;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "🔔 NotificationActivity 시작");

        try {
            setContentView(R.layout.activity_notification);
            Log.d(TAG, "✅ 레이아웃 설정 완료");

            userSession = UserSession.getInstance(this);
            Log.d(TAG, "✅ UserSession 초기화 완료");

            initBasicViews();
            Log.d(TAG, "✅ 기본 뷰 초기화 완료");

            setupBasicToolbar();
            Log.d(TAG, "✅ 기본 툴바 설정 완료");

            setupTabs();
            Log.d(TAG, "✅ 탭 설정 완료");

            // 바텀 네비게이션 설정
            setupBottomNavigation();
            Log.d(TAG, "✅ 바텀 네비게이션 설정 완료");

            // 알림 로드
            loadNotifications();
            Log.d(TAG, "✅ 알림 로드 시작");

        } catch (Exception e) {
            Log.e(TAG, "❌ NotificationActivity 초기화 오류", e);
            e.printStackTrace();
            // 오류가 발생해도 화면을 닫지 않고 빈 상태로 표시
            showEmptyState();
        }
    }

    /**
     * 기본 뷰 초기화
     */
    private void initBasicViews() {
        try {
            recyclerNotifications = findViewById(R.id.recyclerNotifications);
            textEmptyNotifications = findViewById(R.id.layoutEmptyState);
            btnTabPending = findViewById(R.id.btnTabPending);
            btnTabAll = findViewById(R.id.btnTabAll);

            if (recyclerNotifications == null) {
                Log.e(TAG, "recyclerNotifications를 찾을 수 없습니다");
                throw new RuntimeException("recyclerNotifications를 찾을 수 없습니다");
            }

            if (textEmptyNotifications == null) {
                Log.e(TAG, "textEmptyNotifications를 찾을 수 없습니다");
                throw new RuntimeException("textEmptyNotifications를 찾을 수 없습니다");
            }

            // RecyclerView 기본 설정
            recyclerNotifications.setLayoutManager(new LinearLayoutManager(this));

            // 어댑터 초기화
            notificationList = new ArrayList<>();
            adapter = new NotificationAdapter(notificationList, this::handleNotificationAction);
            recyclerNotifications.setAdapter(adapter);

            Log.d(TAG, "기본 뷰 초기화 완료");

        } catch (Exception e) {
            Log.e(TAG, "기본 뷰 초기화 오류", e);
            throw e;
        }
    }

    /**
     * iOS 스타일 툴바 설정
     */
    private void setupBasicToolbar() {
        try {
            ImageButton btnBack = findViewById(R.id.btnBack);
            if (btnBack != null) {
                btnBack.setOnClickListener(v -> {
                    try {
                        Log.d(TAG, "🔙 뒤로가기 버튼 클릭");
                        onBackPressed(); // 뒤로가기 동작
                    } catch (Exception e) {
                        Log.e(TAG, "뒤로가기 오류", e);
                        finish();
                    }
                });
                Log.d(TAG, "✅ 뒤로가기 버튼 활성화 완료");
            } else {
                Log.w(TAG, "⚠️ 뒤로가기 버튼을 찾을 수 없습니다");
            }

            Log.d(TAG, "iOS 스타일 툴바 설정 완료");

        } catch (Exception e) {
            Log.e(TAG, "툴바 설정 오류", e);
        }
    }

    @Override
    public void onBackPressed() {
        try {
            Log.d(TAG, "🔙 onBackPressed 호출 - 알림창 닫기");
            finish();
            // 부드러운 전환 애니메이션
            overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);
        } catch (Exception e) {
            Log.e(TAG, "onBackPressed 오류", e);
            finish();
        }
    }

    /**
     * iOS 스타일 탭 설정
     */
    private void setupTabs() {
        try {
            if (btnTabPending != null && btnTabAll != null) {
                // 대기중 탭 클릭
                btnTabPending.setOnClickListener(v -> {
                    Log.d(TAG, "📋 대기중 탭 선택");
                    selectTab(true);
                    filterNotifications();
                });

                // 전체 탭 클릭
                btnTabAll.setOnClickListener(v -> {
                    Log.d(TAG, "📋 전체 탭 선택");
                    selectTab(false);
                    filterNotifications();
                });

                // 초기 탭 설정 (대기중 선택)
                selectTab(true);

                Log.d(TAG, "✅ 탭 설정 완료");
            } else {
                Log.w(TAG, "⚠️ 탭 버튼을 찾을 수 없습니다");
            }

        } catch (Exception e) {
            Log.e(TAG, "탭 설정 오류", e);
        }
    }

    /**
     * 탭 선택 상태 변경
     */
    private void selectTab(boolean isPendingTab) {
        try {
            isShowingPendingOnly = isPendingTab;

            Log.d(TAG, "탭 선택 변경: " + (isPendingTab ? "대기중" : "전체"));

            if (isPendingTab) {
                // 대기중 탭 선택
                btnTabPending.setBackgroundResource(R.drawable.ios_tab_selected);
                btnTabPending.setTextColor(getResources().getColor(android.R.color.white, getTheme()));
                btnTabPending.setTypeface(null, android.graphics.Typeface.BOLD);

                btnTabAll.setBackgroundResource(R.drawable.ios_tab_unselected);
                btnTabAll.setTextColor(getResources().getColor(R.color.text_secondary, getTheme()));
                btnTabAll.setTypeface(null, android.graphics.Typeface.NORMAL);
            } else {
                // 전체 탭 선택
                btnTabAll.setBackgroundResource(R.drawable.ios_tab_selected);
                btnTabAll.setTextColor(getResources().getColor(android.R.color.white, getTheme()));
                btnTabAll.setTypeface(null, android.graphics.Typeface.BOLD);

                btnTabPending.setBackgroundResource(R.drawable.ios_tab_unselected);
                btnTabPending.setTextColor(getResources().getColor(R.color.text_secondary, getTheme()));
                btnTabPending.setTypeface(null, android.graphics.Typeface.NORMAL);
            }

            Log.d(TAG, "✅ 탭 UI 업데이트 완료: " + (isPendingTab ? "대기중" : "전체"));

        } catch (Exception e) {
            Log.e(TAG, "탭 선택 오류", e);
        }
    }

    /**
     * 알림 필터링
     */
    private void filterNotifications() {
        try {
            if (allNotifications == null) {
                Log.w(TAG, "전체 알림 목록이 null입니다");
                showEmptyState();
                return;
            }

            List<SharedSchedule> filteredNotifications = new ArrayList<>();

            if (isShowingPendingOnly) {
                // 대기중 알림만 표시 (pending 상태)
                for (SharedSchedule notification : allNotifications) {
                    if ("pending".equals(notification.status)) {
                        filteredNotifications.add(notification);
                    }
                }
                Log.d(TAG, "📋 대기중 알림 필터링 완료: " + filteredNotifications.size() + "개");
            } else {
                // 전체 알림 표시 (모든 상태)
                filteredNotifications.addAll(allNotifications);
                Log.d(TAG, "📋 전체 알림 표시: " + filteredNotifications.size() + "개");
            }

            // 현재 알림 목록 업데이트
            notificationList = filteredNotifications;

            // UI 업데이트
            runOnUiThread(() -> {
                if (filteredNotifications.isEmpty()) {
                    showEmptyState();
                } else {
                    showNotifications(filteredNotifications);
                }
            });

            Log.d(TAG, "✅ 알림 필터링 및 UI 업데이트 완료");

        } catch (Exception e) {
            Log.e(TAG, "알림 필터링 오류", e);
            runOnUiThread(() -> showEmptyState());
        }
    }

    /**
     * iOS 스타일 바텀 네비게이션 설정
     */
    private void setupBottomNavigation() {
        try {
            com.google.android.material.bottomnavigation.BottomNavigationView bottomNav =
                findViewById(R.id.bottomNavigationView);

            if (bottomNav != null) {
                // 현재 페이지를 알림으로 설정 (없으므로 홈으로 설정)
                bottomNav.setSelectedItemId(R.id.nav_home);

                bottomNav.setOnItemSelectedListener(item -> {
                    try {
                        int itemId = item.getItemId();
                        Intent intent = null;

                        Log.d(TAG, "🔄 바텀 네비게이션 메뉴 선택: " + item.getTitle());

                        if (itemId == R.id.nav_home) {
                            intent = new Intent(this, com.example.timemate.features.home.HomeActivity.class);
                        } else if (itemId == R.id.nav_friends) {
                            intent = new Intent(this, com.example.timemate.features.friend.FriendListActivity.class);
                        } else if (itemId == R.id.nav_profile) {
                            intent = new Intent(this, com.example.timemate.features.profile.ProfileActivity.class);
                        }

                        if (intent != null) {
                            Log.d(TAG, "📱 바텀 네비게이션 이동 - 알림창 닫기");

                            // 현재 알림창 종료 후 새 화면으로 이동
                            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
                            startActivity(intent);
                            finish();

                            // iOS 스타일 전환 애니메이션
                            overridePendingTransition(android.R.anim.slide_in_left, android.R.anim.slide_out_right);

                            return true;
                        }

                    } catch (Exception e) {
                        Log.e(TAG, "바텀 네비게이션 오류", e);
                    }
                    return false;
                });

                Log.d(TAG, "✅ iOS 스타일 바텀 네비게이션 설정 완료");
            } else {
                Log.w(TAG, "⚠️ 바텀 네비게이션을 찾을 수 없습니다");
            }

        } catch (Exception e) {
            Log.e(TAG, "바텀 네비게이션 설정 오류", e);
        }
    }

    /**
     * 빈 상태 표시
     */
    private void showEmptyState() {
        try {
            if (recyclerNotifications != null) {
                recyclerNotifications.setVisibility(View.GONE);
            }
            if (textEmptyNotifications != null) {
                textEmptyNotifications.setVisibility(View.VISIBLE);
            }

            Log.d(TAG, "빈 상태 표시 완료");

        } catch (Exception e) {
            Log.e(TAG, "빈 상태 표시 오류", e);
        }
    }
    
    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
    
    /**
     * 알림 목록 로드
     */
    private void loadNotifications() {
        String currentUserId = userSession.getCurrentUserId();
        if (currentUserId == null || currentUserId.trim().isEmpty()) {
            Log.w(TAG, "사용자 ID가 null - 기본 사용자 사용");
            currentUserId = "user1"; // 기본 사용자 ID

            // UserSession에 기본 사용자 정보 설정
            userSession.login(currentUserId, "사용자1", "<EMAIL>", true);
            Toast.makeText(this, "기본 사용자(user1)로 설정되었습니다", Toast.LENGTH_SHORT).show();
        }

        final String finalCurrentUserId = currentUserId; // final 변수로 복사

        Executors.newSingleThreadExecutor().execute(() -> {
            try {
                AppDatabase database = AppDatabase.getInstance(this);

                // 모든 공유 일정 초대 알림 로드 (모든 상태)
                List<SharedSchedule> allInvites = database.sharedScheduleDao()
                    .getSharedSchedulesByUserId(finalCurrentUserId);

                Log.d(TAG, "전체 로드된 알림 수: " + (allInvites != null ? allInvites.size() : 0));

                runOnUiThread(() -> {
                    // 전체 알림 목록 저장
                    allNotifications = allInvites != null ? allInvites : new ArrayList<>();

                    // 현재 선택된 탭에 따라 필터링
                    filterNotifications();
                });

            } catch (Exception e) {
                Log.e(TAG, "알림 로드 오류", e);
                runOnUiThread(() -> {
                    Toast.makeText(this, "알림을 불러오는 중 오류가 발생했습니다", Toast.LENGTH_SHORT).show();
                    allNotifications = new ArrayList<>();
                    showEmptyState();
                });
            }
        });
    }


    
    /**
     * 알림 목록 표시
     */
    private void showNotifications(List<SharedSchedule> notifications) {
        recyclerNotifications.setVisibility(View.VISIBLE);
        textEmptyNotifications.setVisibility(View.GONE);

        if (adapter != null) {
            adapter.updateNotifications(notifications);
        }
    }
    
    /**
     * 알림 액션 처리 (수락/거절)
     */
    private void handleNotificationAction(SharedSchedule notification, String action) {
        Log.d(TAG, "알림 액션: " + action + " for notification ID: " + notification.id);

        Executors.newSingleThreadExecutor().execute(() -> {
            try {
                AppDatabase database = AppDatabase.getInstance(this);

                if ("accept".equals(action)) {
                    // 수락 처리
                    notification.status = "accepted";
                    notification.respondedAt = System.currentTimeMillis();
                    notification.updatedAt = System.currentTimeMillis();

                    // 더미 데이터가 아닌 경우에만 데이터베이스 업데이트
                    if (notification.id < 1000) { // 더미 데이터는 1000 이상 ID 사용
                        database.sharedScheduleDao().updateStatus(notification.id, "accepted");
                    }

                    // 수락 시 해당 일정을 내 일정에 추가
                    addAcceptedScheduleToMySchedules(notification);

                    runOnUiThread(() -> {
                        Toast.makeText(this, "✅ 일정 초대를 수락했습니다!\n내 일정에 추가되었습니다.", Toast.LENGTH_LONG).show();
                        loadNotifications(); // 목록 새로고침

                        // 수락 완료 알림 전송 (초대한 사람에게)
                        sendAcceptanceNotification(notification);
                    });

                } else if ("reject".equals(action)) {
                    // 거절 처리
                    notification.status = "rejected";
                    notification.respondedAt = System.currentTimeMillis();
                    notification.updatedAt = System.currentTimeMillis();

                    // 더미 데이터가 아닌 경우에만 데이터베이스 업데이트
                    if (notification.id < 1000) { // 더미 데이터는 1000 이상 ID 사용
                        database.sharedScheduleDao().updateStatus(notification.id, "rejected");
                    }

                    // 원본 일정에서 거절한 친구 정보 제거
                    removeRejectedFriendFromSchedule(notification);

                    runOnUiThread(() -> {
                        Toast.makeText(this, "❌ 일정 초대를 거절했습니다", Toast.LENGTH_SHORT).show();
                        loadNotifications(); // 목록 새로고침

                        // 거절 알림을 초대한 사람에게 전송
                        sendRejectionNotification(notification);
                    });
                }

            } catch (Exception e) {
                Log.e(TAG, "알림 액션 처리 오류", e);
                runOnUiThread(() -> {
                    Toast.makeText(this, "처리 중 오류가 발생했습니다", Toast.LENGTH_SHORT).show();
                });
            }
        });
    }

    /**
     * 수락한 일정을 내 일정에 추가 (공유 일정으로)
     */
    private void addAcceptedScheduleToMySchedules(SharedSchedule sharedSchedule) {
        try {
            AppDatabase database = AppDatabase.getInstance(this);
            String currentUserId = userSession.getCurrentUserId();

            // 새로운 공유 일정 생성
            com.example.timemate.data.model.Schedule newSchedule =
                new com.example.timemate.data.model.Schedule();

            newSchedule.userId = currentUserId;
            newSchedule.title = sharedSchedule.title;
            newSchedule.date = sharedSchedule.date;
            newSchedule.time = sharedSchedule.time;
            newSchedule.departure = sharedSchedule.departure;
            newSchedule.destination = sharedSchedule.destination;
            newSchedule.memo = sharedSchedule.memo + "\n\n👥 " + sharedSchedule.creatorNickname + "님과 공유 중";
            newSchedule.createdAt = System.currentTimeMillis();
            newSchedule.updatedAt = System.currentTimeMillis();

            // 내 일정에 추가
            long newScheduleId = database.scheduleDao().insert(newSchedule);

            // SharedSchedule 업데이트 - 동기화 정보 설정
            sharedSchedule.sharedScheduleId = (int) newScheduleId;
            sharedSchedule.isSyncEnabled = true;
            database.sharedScheduleDao().update(sharedSchedule);

            // 원본 일정에도 공유 정보 추가 (초대한 사람의 일정)
            createReverseSharedSchedule(sharedSchedule, (int) newScheduleId);

            Log.d(TAG, "공유 일정 생성 완료: " + newSchedule.title + " (ID: " + newScheduleId + ")");

        } catch (Exception e) {
            Log.e(TAG, "공유 일정 생성 오류", e);
        }
    }

    /**
     * 역방향 공유 일정 생성 (초대한 사람 → 수락한 사람)
     */
    private void createReverseSharedSchedule(SharedSchedule originalShared, int acceptedScheduleId) {
        try {
            AppDatabase database = AppDatabase.getInstance(this);

            // 역방향 SharedSchedule 생성
            SharedSchedule reverseShared = new SharedSchedule();
            reverseShared.originalScheduleId = acceptedScheduleId;
            reverseShared.creatorUserId = originalShared.invitedUserId;
            reverseShared.creatorNickname = originalShared.invitedNickname;
            reverseShared.invitedUserId = originalShared.creatorUserId;
            reverseShared.invitedNickname = originalShared.creatorNickname;
            reverseShared.title = originalShared.title;
            reverseShared.date = originalShared.date;
            reverseShared.time = originalShared.time;
            reverseShared.departure = originalShared.departure;
            reverseShared.destination = originalShared.destination;
            reverseShared.memo = originalShared.memo;
            reverseShared.status = "accepted";
            reverseShared.sharedScheduleId = originalShared.originalScheduleId;
            reverseShared.isSyncEnabled = true;
            reverseShared.isNotificationSent = true;

            database.sharedScheduleDao().insert(reverseShared);

            Log.d(TAG, "역방향 공유 일정 생성 완료");

        } catch (Exception e) {
            Log.e(TAG, "역방향 공유 일정 생성 오류", e);
        }
    }

    /**
     * 초대 수락 알림을 초대한 사람에게 전송
     */
    private void sendAcceptanceNotification(SharedSchedule sharedSchedule) {
        try {
            com.example.timemate.NotificationService notificationService =
                new com.example.timemate.NotificationService(this);

            String title = "일정 초대 수락됨";
            String content = sharedSchedule.invitedNickname + "님이 '" +
                           sharedSchedule.title + "' 일정 초대를 수락했습니다! 🎉";

            // 알림 ID는 원본 일정 ID + 1000으로 설정 (중복 방지)
            int notificationId = sharedSchedule.originalScheduleId + 1000;

            notificationService.sendGeneralNotification(title, content, notificationId);

            Log.d(TAG, "초대 수락 알림 전송 완료");

        } catch (Exception e) {
            Log.e(TAG, "초대 수락 알림 전송 오류", e);
        }
    }

    /**
     * 거절한 친구를 원본 일정에서 제거
     */
    private void removeRejectedFriendFromSchedule(SharedSchedule rejectedShared) {
        try {
            AppDatabase database = AppDatabase.getInstance(this);

            // 원본 일정 조회
            com.example.timemate.data.model.Schedule originalSchedule =
                database.scheduleDao().getScheduleById(rejectedShared.originalScheduleId);

            if (originalSchedule != null && originalSchedule.memo != null) {
                // 메모에서 거절한 친구 정보 제거
                String friendInfo = "👥 " + rejectedShared.invitedNickname + "님과 함께";
                String updatedMemo = originalSchedule.memo.replace(friendInfo, "").trim();

                // 연속된 줄바꿈 정리
                updatedMemo = updatedMemo.replaceAll("\n\n+", "\n\n");
                if (updatedMemo.endsWith("\n\n")) {
                    updatedMemo = updatedMemo.substring(0, updatedMemo.length() - 2);
                }

                originalSchedule.memo = updatedMemo;
                originalSchedule.updatedAt = System.currentTimeMillis();

                database.scheduleDao().update(originalSchedule);

                Log.d(TAG, "거절한 친구를 원본 일정에서 제거 완료: " + rejectedShared.invitedNickname);
            }

        } catch (Exception e) {
            Log.e(TAG, "거절한 친구 제거 오류", e);
        }
    }

    /**
     * 초대 거절 알림을 초대한 사람에게 전송
     */
    private void sendRejectionNotification(SharedSchedule rejectedShared) {
        try {
            com.example.timemate.NotificationService notificationService =
                new com.example.timemate.NotificationService(this);

            String title = "일정 초대 거절됨";
            String content = rejectedShared.invitedNickname + "님이 '" +
                           rejectedShared.title + "' 일정 초대를 거절했습니다.";

            // 알림 ID는 원본 일정 ID + 2000으로 설정 (중복 방지)
            int notificationId = rejectedShared.originalScheduleId + 2000;

            notificationService.sendGeneralNotification(title, content, notificationId);

            Log.d(TAG, "초대 거절 알림 전송 완료");

        } catch (Exception e) {
            Log.e(TAG, "초대 거절 알림 전송 오류", e);
        }
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        // 화면이 다시 보일 때 알림 목록 새로고침
        loadNotifications();
    }
}
