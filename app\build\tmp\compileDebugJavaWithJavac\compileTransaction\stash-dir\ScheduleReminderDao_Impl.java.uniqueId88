package com.example.timemate;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class ScheduleReminderDao_Impl implements ScheduleReminderDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<ScheduleReminder> __insertionAdapterOfScheduleReminder;

  private final EntityDeletionOrUpdateAdapter<ScheduleReminder> __deletionAdapterOfScheduleReminder;

  private final EntityDeletionOrUpdateAdapter<ScheduleReminder> __updateAdapterOfScheduleReminder;

  private final SharedSQLiteStatement __preparedStmtOfMarkNotificationSent;

  private final SharedSQLiteStatement __preparedStmtOfDeactivateByScheduleId;

  private final SharedSQLiteStatement __preparedStmtOfDeleteByScheduleId;

  public ScheduleReminderDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfScheduleReminder = new EntityInsertionAdapter<ScheduleReminder>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR ABORT INTO `schedule_reminder` (`id`,`schedule_id`,`user_id`,`title`,`appointment_time`,`departure`,`destination`,`optimal_transport`,`duration_minutes`,`recommended_departure_time`,`distance`,`route_summary`,`toll_fare`,`fuel_price`,`created_at`,`notification_sent`,`is_active`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final ScheduleReminder entity) {
        statement.bindLong(1, entity.id);
        statement.bindLong(2, entity.scheduleId);
        if (entity.userId == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.userId);
        }
        if (entity.title == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.title);
        }
        if (entity.appointmentTime == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.appointmentTime);
        }
        if (entity.departure == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.departure);
        }
        if (entity.destination == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.destination);
        }
        if (entity.optimalTransport == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.optimalTransport);
        }
        statement.bindLong(9, entity.durationMinutes);
        if (entity.recommendedDepartureTime == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.recommendedDepartureTime);
        }
        if (entity.distance == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.distance);
        }
        if (entity.routeSummary == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.routeSummary);
        }
        if (entity.tollFare == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.tollFare);
        }
        if (entity.fuelPrice == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.fuelPrice);
        }
        statement.bindLong(15, entity.createdAt);
        final int _tmp = entity.notificationSent ? 1 : 0;
        statement.bindLong(16, _tmp);
        final int _tmp_1 = entity.isActive ? 1 : 0;
        statement.bindLong(17, _tmp_1);
      }
    };
    this.__deletionAdapterOfScheduleReminder = new EntityDeletionOrUpdateAdapter<ScheduleReminder>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `schedule_reminder` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final ScheduleReminder entity) {
        statement.bindLong(1, entity.id);
      }
    };
    this.__updateAdapterOfScheduleReminder = new EntityDeletionOrUpdateAdapter<ScheduleReminder>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `schedule_reminder` SET `id` = ?,`schedule_id` = ?,`user_id` = ?,`title` = ?,`appointment_time` = ?,`departure` = ?,`destination` = ?,`optimal_transport` = ?,`duration_minutes` = ?,`recommended_departure_time` = ?,`distance` = ?,`route_summary` = ?,`toll_fare` = ?,`fuel_price` = ?,`created_at` = ?,`notification_sent` = ?,`is_active` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final ScheduleReminder entity) {
        statement.bindLong(1, entity.id);
        statement.bindLong(2, entity.scheduleId);
        if (entity.userId == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.userId);
        }
        if (entity.title == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.title);
        }
        if (entity.appointmentTime == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.appointmentTime);
        }
        if (entity.departure == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.departure);
        }
        if (entity.destination == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.destination);
        }
        if (entity.optimalTransport == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.optimalTransport);
        }
        statement.bindLong(9, entity.durationMinutes);
        if (entity.recommendedDepartureTime == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.recommendedDepartureTime);
        }
        if (entity.distance == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.distance);
        }
        if (entity.routeSummary == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.routeSummary);
        }
        if (entity.tollFare == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.tollFare);
        }
        if (entity.fuelPrice == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.fuelPrice);
        }
        statement.bindLong(15, entity.createdAt);
        final int _tmp = entity.notificationSent ? 1 : 0;
        statement.bindLong(16, _tmp);
        final int _tmp_1 = entity.isActive ? 1 : 0;
        statement.bindLong(17, _tmp_1);
        statement.bindLong(18, entity.id);
      }
    };
    this.__preparedStmtOfMarkNotificationSent = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE schedule_reminder SET notification_sent = 1 WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeactivateByScheduleId = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE schedule_reminder SET is_active = 0 WHERE schedule_id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteByScheduleId = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM schedule_reminder WHERE schedule_id = ?";
        return _query;
      }
    };
  }

  @Override
  public void insert(final ScheduleReminder reminder) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __insertionAdapterOfScheduleReminder.insert(reminder);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void insertReminder(final ScheduleReminder reminder) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __insertionAdapterOfScheduleReminder.insert(reminder);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void delete(final ScheduleReminder reminder) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __deletionAdapterOfScheduleReminder.handle(reminder);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void update(final ScheduleReminder reminder) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __updateAdapterOfScheduleReminder.handle(reminder);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void markNotificationSent(final int reminderId) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfMarkNotificationSent.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, reminderId);
    try {
      __db.beginTransaction();
      try {
        _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfMarkNotificationSent.release(_stmt);
    }
  }

  @Override
  public void deactivateByScheduleId(final int scheduleId) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeactivateByScheduleId.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, scheduleId);
    try {
      __db.beginTransaction();
      try {
        _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfDeactivateByScheduleId.release(_stmt);
    }
  }

  @Override
  public void deleteByScheduleId(final int scheduleId) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteByScheduleId.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, scheduleId);
    try {
      __db.beginTransaction();
      try {
        _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfDeleteByScheduleId.release(_stmt);
    }
  }

  @Override
  public List<ScheduleReminder> getActiveRemindersByUserId(final String userId) {
    final String _sql = "SELECT * FROM schedule_reminder WHERE user_id = ? AND is_active = 1 ORDER BY appointment_time ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (userId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, userId);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfScheduleId = CursorUtil.getColumnIndexOrThrow(_cursor, "schedule_id");
      final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
      final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
      final int _cursorIndexOfAppointmentTime = CursorUtil.getColumnIndexOrThrow(_cursor, "appointment_time");
      final int _cursorIndexOfDeparture = CursorUtil.getColumnIndexOrThrow(_cursor, "departure");
      final int _cursorIndexOfDestination = CursorUtil.getColumnIndexOrThrow(_cursor, "destination");
      final int _cursorIndexOfOptimalTransport = CursorUtil.getColumnIndexOrThrow(_cursor, "optimal_transport");
      final int _cursorIndexOfDurationMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "duration_minutes");
      final int _cursorIndexOfRecommendedDepartureTime = CursorUtil.getColumnIndexOrThrow(_cursor, "recommended_departure_time");
      final int _cursorIndexOfDistance = CursorUtil.getColumnIndexOrThrow(_cursor, "distance");
      final int _cursorIndexOfRouteSummary = CursorUtil.getColumnIndexOrThrow(_cursor, "route_summary");
      final int _cursorIndexOfTollFare = CursorUtil.getColumnIndexOrThrow(_cursor, "toll_fare");
      final int _cursorIndexOfFuelPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "fuel_price");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfNotificationSent = CursorUtil.getColumnIndexOrThrow(_cursor, "notification_sent");
      final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
      final List<ScheduleReminder> _result = new ArrayList<ScheduleReminder>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final ScheduleReminder _item;
        _item = new ScheduleReminder();
        _item.id = _cursor.getInt(_cursorIndexOfId);
        _item.scheduleId = _cursor.getInt(_cursorIndexOfScheduleId);
        if (_cursor.isNull(_cursorIndexOfUserId)) {
          _item.userId = null;
        } else {
          _item.userId = _cursor.getString(_cursorIndexOfUserId);
        }
        if (_cursor.isNull(_cursorIndexOfTitle)) {
          _item.title = null;
        } else {
          _item.title = _cursor.getString(_cursorIndexOfTitle);
        }
        if (_cursor.isNull(_cursorIndexOfAppointmentTime)) {
          _item.appointmentTime = null;
        } else {
          _item.appointmentTime = _cursor.getString(_cursorIndexOfAppointmentTime);
        }
        if (_cursor.isNull(_cursorIndexOfDeparture)) {
          _item.departure = null;
        } else {
          _item.departure = _cursor.getString(_cursorIndexOfDeparture);
        }
        if (_cursor.isNull(_cursorIndexOfDestination)) {
          _item.destination = null;
        } else {
          _item.destination = _cursor.getString(_cursorIndexOfDestination);
        }
        if (_cursor.isNull(_cursorIndexOfOptimalTransport)) {
          _item.optimalTransport = null;
        } else {
          _item.optimalTransport = _cursor.getString(_cursorIndexOfOptimalTransport);
        }
        _item.durationMinutes = _cursor.getInt(_cursorIndexOfDurationMinutes);
        if (_cursor.isNull(_cursorIndexOfRecommendedDepartureTime)) {
          _item.recommendedDepartureTime = null;
        } else {
          _item.recommendedDepartureTime = _cursor.getString(_cursorIndexOfRecommendedDepartureTime);
        }
        if (_cursor.isNull(_cursorIndexOfDistance)) {
          _item.distance = null;
        } else {
          _item.distance = _cursor.getString(_cursorIndexOfDistance);
        }
        if (_cursor.isNull(_cursorIndexOfRouteSummary)) {
          _item.routeSummary = null;
        } else {
          _item.routeSummary = _cursor.getString(_cursorIndexOfRouteSummary);
        }
        if (_cursor.isNull(_cursorIndexOfTollFare)) {
          _item.tollFare = null;
        } else {
          _item.tollFare = _cursor.getString(_cursorIndexOfTollFare);
        }
        if (_cursor.isNull(_cursorIndexOfFuelPrice)) {
          _item.fuelPrice = null;
        } else {
          _item.fuelPrice = _cursor.getString(_cursorIndexOfFuelPrice);
        }
        _item.createdAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfNotificationSent);
        _item.notificationSent = _tmp != 0;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsActive);
        _item.isActive = _tmp_1 != 0;
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public ScheduleReminder getReminderByScheduleId(final int scheduleId) {
    final String _sql = "SELECT * FROM schedule_reminder WHERE schedule_id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, scheduleId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfScheduleId = CursorUtil.getColumnIndexOrThrow(_cursor, "schedule_id");
      final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
      final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
      final int _cursorIndexOfAppointmentTime = CursorUtil.getColumnIndexOrThrow(_cursor, "appointment_time");
      final int _cursorIndexOfDeparture = CursorUtil.getColumnIndexOrThrow(_cursor, "departure");
      final int _cursorIndexOfDestination = CursorUtil.getColumnIndexOrThrow(_cursor, "destination");
      final int _cursorIndexOfOptimalTransport = CursorUtil.getColumnIndexOrThrow(_cursor, "optimal_transport");
      final int _cursorIndexOfDurationMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "duration_minutes");
      final int _cursorIndexOfRecommendedDepartureTime = CursorUtil.getColumnIndexOrThrow(_cursor, "recommended_departure_time");
      final int _cursorIndexOfDistance = CursorUtil.getColumnIndexOrThrow(_cursor, "distance");
      final int _cursorIndexOfRouteSummary = CursorUtil.getColumnIndexOrThrow(_cursor, "route_summary");
      final int _cursorIndexOfTollFare = CursorUtil.getColumnIndexOrThrow(_cursor, "toll_fare");
      final int _cursorIndexOfFuelPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "fuel_price");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfNotificationSent = CursorUtil.getColumnIndexOrThrow(_cursor, "notification_sent");
      final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
      final ScheduleReminder _result;
      if (_cursor.moveToFirst()) {
        _result = new ScheduleReminder();
        _result.id = _cursor.getInt(_cursorIndexOfId);
        _result.scheduleId = _cursor.getInt(_cursorIndexOfScheduleId);
        if (_cursor.isNull(_cursorIndexOfUserId)) {
          _result.userId = null;
        } else {
          _result.userId = _cursor.getString(_cursorIndexOfUserId);
        }
        if (_cursor.isNull(_cursorIndexOfTitle)) {
          _result.title = null;
        } else {
          _result.title = _cursor.getString(_cursorIndexOfTitle);
        }
        if (_cursor.isNull(_cursorIndexOfAppointmentTime)) {
          _result.appointmentTime = null;
        } else {
          _result.appointmentTime = _cursor.getString(_cursorIndexOfAppointmentTime);
        }
        if (_cursor.isNull(_cursorIndexOfDeparture)) {
          _result.departure = null;
        } else {
          _result.departure = _cursor.getString(_cursorIndexOfDeparture);
        }
        if (_cursor.isNull(_cursorIndexOfDestination)) {
          _result.destination = null;
        } else {
          _result.destination = _cursor.getString(_cursorIndexOfDestination);
        }
        if (_cursor.isNull(_cursorIndexOfOptimalTransport)) {
          _result.optimalTransport = null;
        } else {
          _result.optimalTransport = _cursor.getString(_cursorIndexOfOptimalTransport);
        }
        _result.durationMinutes = _cursor.getInt(_cursorIndexOfDurationMinutes);
        if (_cursor.isNull(_cursorIndexOfRecommendedDepartureTime)) {
          _result.recommendedDepartureTime = null;
        } else {
          _result.recommendedDepartureTime = _cursor.getString(_cursorIndexOfRecommendedDepartureTime);
        }
        if (_cursor.isNull(_cursorIndexOfDistance)) {
          _result.distance = null;
        } else {
          _result.distance = _cursor.getString(_cursorIndexOfDistance);
        }
        if (_cursor.isNull(_cursorIndexOfRouteSummary)) {
          _result.routeSummary = null;
        } else {
          _result.routeSummary = _cursor.getString(_cursorIndexOfRouteSummary);
        }
        if (_cursor.isNull(_cursorIndexOfTollFare)) {
          _result.tollFare = null;
        } else {
          _result.tollFare = _cursor.getString(_cursorIndexOfTollFare);
        }
        if (_cursor.isNull(_cursorIndexOfFuelPrice)) {
          _result.fuelPrice = null;
        } else {
          _result.fuelPrice = _cursor.getString(_cursorIndexOfFuelPrice);
        }
        _result.createdAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfNotificationSent);
        _result.notificationSent = _tmp != 0;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsActive);
        _result.isActive = _tmp_1 != 0;
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<ScheduleReminder> getRemindersByDate(final String date) {
    final String _sql = "SELECT * FROM schedule_reminder WHERE appointment_time LIKE ? || '%' AND is_active = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (date == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, date);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfScheduleId = CursorUtil.getColumnIndexOrThrow(_cursor, "schedule_id");
      final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
      final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
      final int _cursorIndexOfAppointmentTime = CursorUtil.getColumnIndexOrThrow(_cursor, "appointment_time");
      final int _cursorIndexOfDeparture = CursorUtil.getColumnIndexOrThrow(_cursor, "departure");
      final int _cursorIndexOfDestination = CursorUtil.getColumnIndexOrThrow(_cursor, "destination");
      final int _cursorIndexOfOptimalTransport = CursorUtil.getColumnIndexOrThrow(_cursor, "optimal_transport");
      final int _cursorIndexOfDurationMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "duration_minutes");
      final int _cursorIndexOfRecommendedDepartureTime = CursorUtil.getColumnIndexOrThrow(_cursor, "recommended_departure_time");
      final int _cursorIndexOfDistance = CursorUtil.getColumnIndexOrThrow(_cursor, "distance");
      final int _cursorIndexOfRouteSummary = CursorUtil.getColumnIndexOrThrow(_cursor, "route_summary");
      final int _cursorIndexOfTollFare = CursorUtil.getColumnIndexOrThrow(_cursor, "toll_fare");
      final int _cursorIndexOfFuelPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "fuel_price");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfNotificationSent = CursorUtil.getColumnIndexOrThrow(_cursor, "notification_sent");
      final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
      final List<ScheduleReminder> _result = new ArrayList<ScheduleReminder>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final ScheduleReminder _item;
        _item = new ScheduleReminder();
        _item.id = _cursor.getInt(_cursorIndexOfId);
        _item.scheduleId = _cursor.getInt(_cursorIndexOfScheduleId);
        if (_cursor.isNull(_cursorIndexOfUserId)) {
          _item.userId = null;
        } else {
          _item.userId = _cursor.getString(_cursorIndexOfUserId);
        }
        if (_cursor.isNull(_cursorIndexOfTitle)) {
          _item.title = null;
        } else {
          _item.title = _cursor.getString(_cursorIndexOfTitle);
        }
        if (_cursor.isNull(_cursorIndexOfAppointmentTime)) {
          _item.appointmentTime = null;
        } else {
          _item.appointmentTime = _cursor.getString(_cursorIndexOfAppointmentTime);
        }
        if (_cursor.isNull(_cursorIndexOfDeparture)) {
          _item.departure = null;
        } else {
          _item.departure = _cursor.getString(_cursorIndexOfDeparture);
        }
        if (_cursor.isNull(_cursorIndexOfDestination)) {
          _item.destination = null;
        } else {
          _item.destination = _cursor.getString(_cursorIndexOfDestination);
        }
        if (_cursor.isNull(_cursorIndexOfOptimalTransport)) {
          _item.optimalTransport = null;
        } else {
          _item.optimalTransport = _cursor.getString(_cursorIndexOfOptimalTransport);
        }
        _item.durationMinutes = _cursor.getInt(_cursorIndexOfDurationMinutes);
        if (_cursor.isNull(_cursorIndexOfRecommendedDepartureTime)) {
          _item.recommendedDepartureTime = null;
        } else {
          _item.recommendedDepartureTime = _cursor.getString(_cursorIndexOfRecommendedDepartureTime);
        }
        if (_cursor.isNull(_cursorIndexOfDistance)) {
          _item.distance = null;
        } else {
          _item.distance = _cursor.getString(_cursorIndexOfDistance);
        }
        if (_cursor.isNull(_cursorIndexOfRouteSummary)) {
          _item.routeSummary = null;
        } else {
          _item.routeSummary = _cursor.getString(_cursorIndexOfRouteSummary);
        }
        if (_cursor.isNull(_cursorIndexOfTollFare)) {
          _item.tollFare = null;
        } else {
          _item.tollFare = _cursor.getString(_cursorIndexOfTollFare);
        }
        if (_cursor.isNull(_cursorIndexOfFuelPrice)) {
          _item.fuelPrice = null;
        } else {
          _item.fuelPrice = _cursor.getString(_cursorIndexOfFuelPrice);
        }
        _item.createdAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfNotificationSent);
        _item.notificationSent = _tmp != 0;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsActive);
        _item.isActive = _tmp_1 != 0;
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<ScheduleReminder> getPendingNotifications() {
    final String _sql = "SELECT * FROM schedule_reminder WHERE notification_sent = 0 AND is_active = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfScheduleId = CursorUtil.getColumnIndexOrThrow(_cursor, "schedule_id");
      final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
      final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
      final int _cursorIndexOfAppointmentTime = CursorUtil.getColumnIndexOrThrow(_cursor, "appointment_time");
      final int _cursorIndexOfDeparture = CursorUtil.getColumnIndexOrThrow(_cursor, "departure");
      final int _cursorIndexOfDestination = CursorUtil.getColumnIndexOrThrow(_cursor, "destination");
      final int _cursorIndexOfOptimalTransport = CursorUtil.getColumnIndexOrThrow(_cursor, "optimal_transport");
      final int _cursorIndexOfDurationMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "duration_minutes");
      final int _cursorIndexOfRecommendedDepartureTime = CursorUtil.getColumnIndexOrThrow(_cursor, "recommended_departure_time");
      final int _cursorIndexOfDistance = CursorUtil.getColumnIndexOrThrow(_cursor, "distance");
      final int _cursorIndexOfRouteSummary = CursorUtil.getColumnIndexOrThrow(_cursor, "route_summary");
      final int _cursorIndexOfTollFare = CursorUtil.getColumnIndexOrThrow(_cursor, "toll_fare");
      final int _cursorIndexOfFuelPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "fuel_price");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfNotificationSent = CursorUtil.getColumnIndexOrThrow(_cursor, "notification_sent");
      final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
      final List<ScheduleReminder> _result = new ArrayList<ScheduleReminder>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final ScheduleReminder _item;
        _item = new ScheduleReminder();
        _item.id = _cursor.getInt(_cursorIndexOfId);
        _item.scheduleId = _cursor.getInt(_cursorIndexOfScheduleId);
        if (_cursor.isNull(_cursorIndexOfUserId)) {
          _item.userId = null;
        } else {
          _item.userId = _cursor.getString(_cursorIndexOfUserId);
        }
        if (_cursor.isNull(_cursorIndexOfTitle)) {
          _item.title = null;
        } else {
          _item.title = _cursor.getString(_cursorIndexOfTitle);
        }
        if (_cursor.isNull(_cursorIndexOfAppointmentTime)) {
          _item.appointmentTime = null;
        } else {
          _item.appointmentTime = _cursor.getString(_cursorIndexOfAppointmentTime);
        }
        if (_cursor.isNull(_cursorIndexOfDeparture)) {
          _item.departure = null;
        } else {
          _item.departure = _cursor.getString(_cursorIndexOfDeparture);
        }
        if (_cursor.isNull(_cursorIndexOfDestination)) {
          _item.destination = null;
        } else {
          _item.destination = _cursor.getString(_cursorIndexOfDestination);
        }
        if (_cursor.isNull(_cursorIndexOfOptimalTransport)) {
          _item.optimalTransport = null;
        } else {
          _item.optimalTransport = _cursor.getString(_cursorIndexOfOptimalTransport);
        }
        _item.durationMinutes = _cursor.getInt(_cursorIndexOfDurationMinutes);
        if (_cursor.isNull(_cursorIndexOfRecommendedDepartureTime)) {
          _item.recommendedDepartureTime = null;
        } else {
          _item.recommendedDepartureTime = _cursor.getString(_cursorIndexOfRecommendedDepartureTime);
        }
        if (_cursor.isNull(_cursorIndexOfDistance)) {
          _item.distance = null;
        } else {
          _item.distance = _cursor.getString(_cursorIndexOfDistance);
        }
        if (_cursor.isNull(_cursorIndexOfRouteSummary)) {
          _item.routeSummary = null;
        } else {
          _item.routeSummary = _cursor.getString(_cursorIndexOfRouteSummary);
        }
        if (_cursor.isNull(_cursorIndexOfTollFare)) {
          _item.tollFare = null;
        } else {
          _item.tollFare = _cursor.getString(_cursorIndexOfTollFare);
        }
        if (_cursor.isNull(_cursorIndexOfFuelPrice)) {
          _item.fuelPrice = null;
        } else {
          _item.fuelPrice = _cursor.getString(_cursorIndexOfFuelPrice);
        }
        _item.createdAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfNotificationSent);
        _item.notificationSent = _tmp != 0;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsActive);
        _item.isActive = _tmp_1 != 0;
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public int getTomorrowRemindersCount(final String userId, final String tomorrow) {
    final String _sql = "SELECT COUNT(*) FROM schedule_reminder WHERE user_id = ? AND appointment_time LIKE ? || '%' AND is_active = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (userId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, userId);
    }
    _argIndex = 2;
    if (tomorrow == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, tomorrow);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if (_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
